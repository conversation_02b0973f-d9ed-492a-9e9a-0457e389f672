import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  ResponsiveImage,
  Alert,
  OTPInput,
  IconCard,
} from '@/shared/components/common';
import { VerifyAccountInfo, User } from '../types/auth.types';
import logoImage from '@/shared/assets/images/logo/logo.png';
import { useAuthCommon } from '@/shared/hooks';
import { useVerifyOtp, useResendOtp } from '../hooks/useAuthQuery';
import CountdownTimer from '../components/CountdownTimer';

/**
 * Verify email page component
 */
const VerifyEmailPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'translation']);
  const navigate = useNavigate();
  const { verifyToken, verifyInfo, verifyExpiresAt, setUserAuth, saveVerifyInfo } = useAuthCommon();
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);
  // State để lưu trữ giá trị verifyExpiresAt từ localStorage
  const [localExpiresAt, setLocalExpiresAt] = useState<number | null>(null);

  // Sử dụng hook để xác thực OTP
  const { mutate: verifyOtp, isPending: isVerifying } = useVerifyOtp();

  // Sử dụng hook để gửi lại OTP
  const { mutate: resendOtp, isPending: isResending } = useResendOtp();

  // Kiểm tra dữ liệu xác thực từ Redux
  useEffect(() => {
    // Log thông tin xác thực khi component mount
    console.log('VerifyEmailPage mounted, auth info:', {
      verifyToken,
      verifyExpiresAt,
      formattedExpiresAt: verifyExpiresAt ? new Date(verifyExpiresAt).toLocaleString() : 'N/A',
      now: new Date().toLocaleString(),
      timeLeft: verifyExpiresAt
        ? Math.floor((verifyExpiresAt - Date.now()) / 1000) + ' seconds'
        : 'N/A',
    });

    if (!verifyToken) {
      // Redirect to login if no token
      navigate('/auth');
    }
  }, [verifyToken, verifyExpiresAt, navigate]);

  // Kiểm tra localStorage khi component mount
  useEffect(() => {
    // Kiểm tra localStorage trực tiếp
    try {
      const persistedAuth = localStorage.getItem('persist:auth');
      if (persistedAuth) {
        const parsedAuth = JSON.parse(persistedAuth);
        console.log('VerifyEmailPage checking localStorage persist:auth:', {
          verifyExpiresAt: parsedAuth.verifyExpiresAt,
        });

        // Nếu verifyExpiresAt là null nhưng có trong localStorage, sử dụng giá trị từ localStorage
        if (!verifyExpiresAt && parsedAuth.verifyExpiresAt) {
          try {
            const parsedExpiresAt = JSON.parse(parsedAuth.verifyExpiresAt);
            if (parsedExpiresAt && !isNaN(parsedExpiresAt)) {
              console.log('Using verifyExpiresAt from localStorage:', parsedExpiresAt);
              // Lưu vào state local để sử dụng
              setLocalExpiresAt(parsedExpiresAt);
            }
          } catch (e) {
            console.error('Error parsing verifyExpiresAt from localStorage:', e);
          }
        }
      }
    } catch (error) {
      console.error('Error checking localStorage:', error);
    }
  }, [verifyExpiresAt]);

  // Kiểm tra thời gian hết hạn của OTP
  useEffect(() => {
    // Sử dụng verifyExpiresAt từ Redux hoặc localExpiresAt từ localStorage
    const expiresAt = verifyExpiresAt || localExpiresAt;
    console.log('Checking expiration with expiresAt:', expiresAt);

    if (expiresAt) {
      // Kiểm tra ngay lập tức
      const checkExpiration = () => {
        const now = Date.now();
        console.log('Checking expiration:', { now, expiresAt, diff: expiresAt - now });

        if (now >= expiresAt) {
          setIsOtpExpired(true);
        } else {
          setIsOtpExpired(false);
        }
      };

      // Kiểm tra ngay lập tức
      checkExpiration();

      // Kiểm tra mỗi giây
      const timer = setInterval(checkExpiration, 1000);

      return () => clearInterval(timer);
    } else {
      return undefined;
    }
  }, [verifyExpiresAt, localExpiresAt]);

  // Xử lý khi OTP thay đổi - được sử dụng trực tiếp trong OTPInput
  // Không cần hàm riêng vì đã sử dụng setOtp trực tiếp

  // Hàm xác thực OTP
  const handleVerifyOtp = (otpValue: string = otp) => {
    if (!verifyToken) return;
    if (!otpValue || otpValue.length !== 6) {
      setError(t('auth:invalidOtp', 'Mã OTP không hợp lệ. Vui lòng nhập đủ 6 chữ số.'));
      return;
    }

    // Không cần xác định platform vì API verifyOtp không cần

    // Gọi API xác thực OTP - chỉ gửi các trường mà backend cần
    // Sử dụng verifyToken hiện tại từ Redux, có thể đã được cập nhật sau khi gọi API resend-otp
    verifyOtp(
      {
        otpToken: verifyToken, // Luôn sử dụng token mới nhất từ Redux
        otp: otpValue,
        // Không gửi trường platform vì backend không cần
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Lưu thông tin đăng nhập vào Redux
            if (response.result) {
              // Lưu accessToken, expiresIn và thông tin user
              // Build auth data conditionally to avoid undefined values
              const authData: {
                accessToken: string;
                expiresIn?: number;
                expiresAt?: number;
                user?: User;
              } = {
                accessToken: response.result.accessToken,
              };

              // Only include optional properties if they have values
              if (response.result.expiresIn !== undefined) {
                authData.expiresIn = response.result.expiresIn;
              }

              if (response.result.expiresAt !== undefined) {
                authData.expiresAt = response.result.expiresAt;
              }

              if (response.result.user !== undefined) {
                authData.user = response.result.user as User;
              }

              setUserAuth(authData);
            }

            // Chuyển hướng đến trang chủ
            navigate('/');
          }
        },
        onError: error => {
          console.error('Verify OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:verifyOtpError', 'Xác thực OTP thất bại');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }
          setError(errorMsg);
        },
      }
    );
  };

  // Hàm gửi lại mã xác thực
  const handleResendCode = () => {
    if (!verifyToken) return;
    setError(null);

    // Không cần xác định platform vì API resendOtp không cần

    // Gọi API gửi lại OTP
    resendOtp(
      {
        otpToken: verifyToken,
        // Không gửi trường platform vì backend không cần
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Lưu otpToken mới vào Redux
            if (response.result && response.result.otpToken) {
              // Lưu thông tin xác thực mới vào Redux
              saveVerifyInfo({
                verifyToken: response.result.otpToken,
                expiresAt: response.result.expiresAt,
                info: [], // API không trả về info, nên dùng mảng rỗng
              });

              // Reset trạng thái hết hạn
              setIsOtpExpired(false);

              // Log để debug
              console.log('Đã cập nhật otpToken mới:', response.result.otpToken);
            }
          }
        },
        onError: error => {
          console.error('Resend OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:resendFailed', 'Không thể gửi lại mã xác thực. Vui lòng thử lại.');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  // Lấy thông tin phương thức xác thực
  const typedVerifyInfo = (verifyInfo as VerifyAccountInfo[]) || [];
  const emailInfo = typedVerifyInfo.find(info => info.platform === 'EMAIL');
  const phoneInfo = typedVerifyInfo.find(info => info.platform === 'SMS');

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-8">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-10">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-8 object-contain max-w-[120px]"
          />
        </div>
      </div>

      <div className="space-y-6 text-center">
        {error && <Alert type="error" message={error} closable onClose={() => setError(null)} />}

        <>
          <div>
            <Typography variant="h4" className="mb-2">
              {t('auth:verifyAccount', 'Xác thực tài khoản')}
            </Typography>
            <Typography variant="body2" color="muted" className="mb-2">
              {emailInfo
                ? t(
                    'auth:verifyEmailDescription',
                    'Chúng tôi đã gửi mã xác thực đến email {{email}}. Vui lòng kiểm tra hộp thư đến của bạn.',
                    { email: emailInfo.value }
                  )
                : phoneInfo
                  ? t(
                      'auth:verifySmsDescription',
                      'Chúng tôi đã gửi mã xác thực đến số điện thoại {{phone}}. Vui lòng kiểm tra tin nhắn của bạn.',
                      { phone: phoneInfo.value }
                    )
                  : t(
                      'auth:verifyDescription',
                      'Chúng tôi đã gửi mã xác thực. Vui lòng kiểm tra email hoặc tin nhắn của bạn.'
                    )}
            </Typography>

            {/* Hiển thị đồng hồ đếm ngược */}
            {verifyExpiresAt || localExpiresAt ? (
              <CountdownTimer
                expiresAt={verifyExpiresAt || localExpiresAt || 0}
                onExpire={() => setIsOtpExpired(true)}
                className="mb-4"
              />
            ) : (
              <></>
            )}
          </div>

          <div className="space-y-6">
            <div>
              <OTPInput
                length={6}
                onChange={setOtp}
                onEnterPress={handleVerifyOtp}
                autoFocus
                className="mb-4"
                disabled={isOtpExpired}
              />
            </div>

            {isOtpExpired ? (
              <Alert
                type="warning"
                message={t('auth:otpExpiredMessage', 'Mã OTP đã hết hạn. Vui lòng gửi lại mã mới.')}
                className="mb-4"
              />
            ) : null}

            <Button
              type="button"
              variant="primary"
              fullWidth
              onClick={() => handleVerifyOtp()}
              isLoading={isVerifying}
              disabled={otp.length !== 6 || isOtpExpired}
            >
              {t('auth:verify', 'Xác thực')}
            </Button>

            <div className="flex flex-col space-y-4 mt-4">
              <Button
                variant="outline"
                fullWidth
                onClick={handleResendCode}
                isLoading={isResending}
              >
                {t('auth:resendCode', 'Gửi lại mã xác thực')}
              </Button>

              <Link to="/auth">
                <IconCard
                  icon="arrow-left"
                  title={t('auth:backToLogin', 'Quay lại đăng nhập')}
                  className="cursor-pointer"
                />
              </Link>
            </div>
          </div>
        </>
      </div>
    </Card>
  );
};

export default VerifyEmailPage;
