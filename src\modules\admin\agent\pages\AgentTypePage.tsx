import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination, SlideInForm } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { AdminAgentTypeGrid, AddAgentTypeForm } from '../components';
import EditAgentTypeForm from '../components/EditAgentTypeForm';
import { useAdminTypeAgents, useAdminTypeAgentDetail, useUpdateAdminTypeAgent } from '../agent-type/hooks/useTypeAgent';
import { CreateTypeAgentParams, UpdateTypeAgentParams } from '../agent-type/types/type-agent.types';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { adminTypeAgentService } from '../agent-type/services/type-agent.service';

/**
 * Trang hiển thị danh sách Agent Types
 */
const AgentTypePage: React.FC = () => {
  const { t } = useTranslation(['admin-agent']);
  const {
    createSuccess,
    updateSuccess,
  } = useAdminAgentNotification();
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Form states
  const [isCreateFormVisible, setIsCreateFormVisible] = useState(false);
  const [isEditFormVisible, setIsEditFormVisible] = useState(false);
  const [editingTypeId, setEditingTypeId] = useState<number | null>(null);

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || '',
  };

  // Lấy danh sách type agents
  const { data: typesResponse, isLoading, error, refetch } = useAdminTypeAgents(queryParams);

  // Lấy thông tin chi tiết type agent để edit
  const { data: editingType } = useAdminTypeAgentDetail(editingTypeId || 0);

  // Hook để update type agent
  const updateTypeMutation = useUpdateAdminTypeAgent();

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddType = () => {
    setIsCreateFormVisible(true);
  };

  const handleViewTrash = () => {
    navigate('/admin/agent/type/trash');
  };

  // Form handlers
  const hideCreateForm = () => setIsCreateFormVisible(false);

  const handleEditType = (typeId: number) => {
    setEditingTypeId(typeId);
    setIsEditFormVisible(true);
  };

  const hideEditForm = () => {
    setIsEditFormVisible(false);
    setEditingTypeId(null);
  };

  // Handle form submission
  const handleSubmitCreateType = async (values: CreateTypeAgentParams) => {
    try {
      const response = await adminTypeAgentService.createTypeAgent(values);

      console.log('🔍 [AgentTypePage] API response:', response);

      return {
        result: response
      };
    } catch (error) {
      console.error('Error creating agent type:', error);
      throw error;
    }
  };

  // Handle update form submission
  const handleSubmitUpdateType = async (id: number, values: UpdateTypeAgentParams) => {
    try {
      const response = await updateTypeMutation.mutateAsync({ id, data: values });

      console.log('🔍 [AgentTypePage] Update API response:', response);

      return {
        result: {
          id: id
        }
      };
    } catch (error) {
      console.error('Error updating agent type:', error);
      throw error;
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const types = useMemo(() => {
    if (!typesResponse?.items) {
      return [];
    }

    console.log('🔍 [AgentTypePage] API Response:', typesResponse.items);

    return typesResponse.items;
  }, [typesResponse]);

  const totalItems = typesResponse?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddType}
          items={[]}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('admin:agent.type.viewTrash', 'Xem thùng rác'),
              variant: 'default',
              onClick: handleViewTrash,
              className: 'text-gray-600 hover:text-gray-800',
            }
          ]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddType}
          items={[]}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('admin:agent.type.viewTrash', 'Xem thùng rác'),
              variant: 'default',
              onClick: handleViewTrash,
              className: 'text-gray-600 hover:text-gray-800',
            }
          ]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={t('admin:agent.type.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddType}
        items={[]}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('admin:agent.type.viewTrash', 'Xem thùng rác'),
            variant: 'default',
            onClick: handleViewTrash,
            className: 'text-gray-600 hover:text-gray-800',
          }
        ]}
      />

       {/* SlideInForm for Create Agent Type */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <AddAgentTypeForm
          onSubmit={handleSubmitCreateType}
          onCancel={hideCreateForm}
          onSuccess={() => {
            hideCreateForm();
            createSuccess(t('admin:agent.type.pageTitle', 'Loại Agent'));
            refetch(); // Refresh the list
          }}
        />
      </SlideInForm>

      {/* SlideInForm for Edit Agent Type */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingType && (
          <EditAgentTypeForm
            agentType={editingType}
            onSubmit={handleSubmitUpdateType}
            onCancel={hideEditForm}
            onSuccess={() => {
              hideEditForm();
              updateSuccess(t('admin:agent.type.pageTitle', 'Loại Agent'));
              refetch(); // Refresh the list
            }}
          />
        )}
      </SlideInForm>

      {types.length > 0 ? (
        <>
          <AdminAgentTypeGrid types={types} onEditType={handleEditType} onSuccess={refetch} />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="settings"
          title={t('admin:agent.type.list.noTypes')}
          description={
            search
              ? t('admin:agent.type.noSearchResults')
              : t('admin:agent.type.list.noTypesDescription')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddType}
            >
              {t('admin:agent.type.addType')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentTypePage;
