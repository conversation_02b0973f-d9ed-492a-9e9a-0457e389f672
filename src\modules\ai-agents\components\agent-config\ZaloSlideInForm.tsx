import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Card,
  Icon,
  IconCard,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { useAgentZaloOfficialAccounts, useAddZaloOfficialAccounts } from '../../hooks/useAgentResources';

/**
 * Interface cho Zalo Official Account
 */
export interface ZaloOfficialAccount {
  id: number;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho query parameters
 */
interface ZaloOfficialAccountQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'active' | 'inactive';
}

/**
 * API function để lấy danh sách Zalo Official Accounts
 */
const getZaloOfficialAccounts = async (
  params: ZaloOfficialAccountQueryParams = {}
): Promise<ApiResponseDto<PaginatedResult<ZaloOfficialAccount>>> => {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.search) queryParams.append('search', params.search);
  if (params.status) queryParams.append('status', params.status);

  return apiClient.get(`/marketing/zalo/paginated?${queryParams.toString()}`);
};

/**
 * Props cho component ZaloSlideInForm
 */
interface ZaloSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * ID của agent (cần thiết cho mode edit)
   */
  agentId?: string | undefined;

  /**
   * Callback khi có Zalo OA được thêm/cập nhật
   */
  onSave?: (accountIds: number[]) => void;

  /**
   * Danh sách Zalo OA IDs đã chọn ban đầu
   */
  initialSelectedIds?: number[];
}

/**
 * Component form trượt để chọn các Zalo Official Accounts
 */
const ZaloSlideInForm: React.FC<ZaloSlideInFormProps> = ({
  isVisible,
  onClose,
  mode,
  agentId,
  onSave,
  initialSelectedIds = [],
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    updateSuccess,
    updateError,
    validationError,
    success
  } = useAiAgentNotification();

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // API hooks cho mode edit
  const { data: agentZaloResponse } = useAgentZaloOfficialAccounts(agentId && mode === 'edit' ? agentId : '');
  const addZaloMutation = useAddZaloOfficialAccounts();

  // Lấy danh sách Zalo đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedZaloIds = React.useMemo(() => {
    return agentZaloResponse?.result?.items?.map((item: { id: number }) => item.id) || [];
  }, [agentZaloResponse?.result?.items]);

  // Khởi tạo selectedIds từ agent hoặc initialSelectedIds
  useEffect(() => {
    if (mode === 'edit' && selectedZaloIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedZaloIds);
    } else if (mode === 'create' && initialSelectedIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedIds);
    }
  }, [selectedZaloIds, mode, selectedIds.length, initialSelectedIds]);

  // API hooks
  const { data: accountsResponse, isLoading } = useQuery({
    queryKey: ['zalo-official-accounts', page, limit, searchTerm],
    queryFn: () => getZaloOfficialAccounts({
      page,
      limit,
      ...(searchTerm && { search: searchTerm })
    }),
    enabled: isVisible,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Debug API response
  console.log('🔍 ZaloSlideInForm API Debug:', {
    accountsResponse,
    isLoading,
    timestamp: new Date().toISOString()
  });

  // Lấy dữ liệu từ API response
  const accounts: ZaloOfficialAccount[] = accountsResponse?.result?.items || [];

  // Filter accounts based on search term
  const filteredAccounts = accounts.filter(account =>
    account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.oaId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Cấu hình cột cho bảng
  const columns: TableColumn<ZaloOfficialAccount>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'account',
      title: t('aiAgents:zaloSlideInForm.account'),
      dataIndex: 'name',
      width: '30%',
      render: (_, record) => (
        <div className="flex items-center">
          {/* Avatar */}
          <div className="w-8 h-8 rounded-full overflow-hidden mr-3 bg-gray-200 dark:bg-gray-700 flex-shrink-0">
            {record.avatarUrl ? (
              <img
                src={record.avatarUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Icon name="user" size="sm" className="text-gray-400" />
              </div>
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.description?.slice(0, 100)}{record.description && record.description.length > 100 && '...'}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'oaId',
      title: 'OA ID',
      dataIndex: 'oaId',
      width: '20%',
      render: (_, record) => (
        <Typography variant="body2" className="font-mono text-sm">
          {record.oaId}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('aiAgents:zaloSlideInForm.status'),
      dataIndex: 'status',
      width: '15%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              {t('aiAgents:zaloSlideInForm.active')}
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="x-circle" size="sm" className="mr-1" />
              {t('aiAgents:zaloSlideInForm.inactive')}
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: t('aiAgents:zaloSlideInForm.createdAt'),
      dataIndex: 'createdAt',
      width: '20%',
      render: (_, record) => {
        const formatDate = (timestamp: string) => {
          try {
            const date = new Date(parseInt(timestamp));
            return date.toLocaleDateString('vi-VN');
          } catch {
            return timestamp;
          }
        };
        return (
          <Typography variant="body2" className="text-gray-600">
            {formatDate(record.createdAt)}
          </Typography>
        );
      },
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentId) {
        validationError(t('aiAgents:zaloSlideInForm.cannotSaveInThisMode'));
        return;
      }

      setIsSubmitting(true);
      try {
        // Gọi API để cập nhật Zalo cho agent
        await addZaloMutation.mutateAsync({
          agentId,
          data: { zaloOfficialAccountIds: selectedIds }
        });

        updateSuccess(t('aiAgents:zaloOfficialAccountConfig.title'));

        onClose();
      } catch (error) {
        updateError(t('aiAgents:zaloOfficialAccountConfig.title'), error instanceof Error ? error.message : undefined);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách account IDs đã chọn
      if (onSave) {
        onSave(selectedIds);
      }

      success({
        message: t('aiAgents:zaloSlideInForm.addAccountsToListSuccess'),
      });

      onClose();
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    setSearchTerm('');
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'filter-active',
      label: t('aiAgents:zaloSlideInForm.filterBy'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'active-only',
      label: t('aiAgents:zaloSlideInForm.active'),
      onClick: () => { },
    },
    {
      id: 'all',
      label: t('aiAgents:zaloSlideInForm.all'),
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:zaloSlideInForm.title')}</Typography>
          <div className="flex justify-end space-x-2">
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('aiAgents:zaloSlideInForm.cancel')}
              onClick={handleClose}
              disabled={isSubmitting}
            />
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={t('aiAgents:zaloSlideInForm.save')}
              onClick={handleSave}
              isLoading={isSubmitting}
              disabled={isLoading || isSubmitting || (mode === 'edit' && !agentId) || selectedIds.length === 0}
            />

          </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<ZaloOfficialAccount>
            columns={columns}
            data={filteredAccounts}
            rowKey="id"
            loading={isLoading}
            sortable={false}
            rowSelection={{
              selectedRowKeys: selectedIds.map(id => id.toString()),
              onChange: (keys) => setSelectedIds(keys.map(key => parseInt(key as string))),
            }}
            pagination={{
              current: page,
              pageSize: limit,
              total: filteredAccounts.length,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
              onChange: (newPage) => setPage(newPage),
            }}
          />
        </div>

        {/* Nút lưu */}

      </Card>
    </SlideInForm>
  );
};

export default ZaloSlideInForm;
