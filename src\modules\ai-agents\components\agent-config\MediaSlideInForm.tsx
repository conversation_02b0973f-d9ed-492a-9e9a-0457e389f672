import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  Icon,
  IconCard,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { Media } from '@/modules/ai-agents/types/response';
import { useMediaList } from '@/modules/data/media/hooks/useMediaQuery';
import { MediaQueryDto } from '@/modules/data/media/types/media.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useAgentMedia, useAddMedia } from '@/modules/ai-agents/hooks/useAgentResources';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component MediaSlideInForm
 */
interface MediaSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * Callback khi có tài nguyên được thêm (chỉ dùng cho mode create)
   */
  onResourceAdded?: (items: Media[]) => void;

  /**
   * Danh sách items đã chọn ban đầu (chỉ dùng cho mode create)
   */
  initialSelectedItems?: Media[];
}

/**
 * Component form trượt để chọn các media
 */
const MediaSlideInForm: React.FC<MediaSlideInFormProps> = ({
  isVisible,
  onClose,
  agentId,
  mode,
  onResourceAdded,
  initialSelectedItems = [],
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    updateSuccess,
    updateError,
    validationError,
    success
  } = useAiAgentNotification();

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  // const [hasChanges, setHasChanges] = useState<boolean>(false);

  // API hooks
  const { data: agentMediaResponse } = useAgentMedia(agentId && mode === 'edit' ? agentId : '');
  const addMediaMutation = useAddMedia();

  // Lấy danh sách media đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedMediaIds = React.useMemo(() => {
    return agentMediaResponse?.items?.map(item => item.id) || [];
  }, [agentMediaResponse?.items]);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<MediaQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: 'createdAt',
    sortDirection: SortDirection.DESC,
  });

  // Khởi tạo selectedIds từ agent media hoặc initialSelectedItems
  useEffect(() => {
    if (mode === 'edit' && selectedMediaIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedMediaIds);
    } else if (mode === 'create' && initialSelectedItems.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedItems.map(item => String(item.id)));
    }
  }, [selectedMediaIds, mode, selectedIds.length, initialSelectedItems]);

  // API hook để lấy danh sách media
  const { data: mediaResponse, isLoading } = useMediaList(queryParams);

  // Lấy dữ liệu từ API response và chuyển đổi sang format Media
  const mediaItems: Media[] = (mediaResponse?.items || []).map(item => ({
    id: item.id,
    name: item.name,
    type: item.viewUrl?.includes('image') ? 'image' as const :
          item.viewUrl?.includes('video') ? 'video' as const :
          item.viewUrl?.includes('audio') ? 'audio' as const : 'document' as const,
    url: item.viewUrl || '',
    thumbnailUrl: item.viewUrl || '',
    fileSize: item.size,
    createdAt: new Date(item.createdAt * 1000).toISOString().split('T')[0] || '',
    format: item.name.split('.').pop() || '',
  }));

  const totalItems = mediaResponse?.meta?.totalItems || 0;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Media>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'media',
      title: t('aiAgents:mediaSlideInForm.media'),
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.thumbnailUrl ? (
              <img
                src={record.thumbnailUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon
                name={
                  record.type === 'image' ? 'image' :
                  record.type === 'video' ? 'video' :
                  record.type === 'audio' ? 'music' : 'file'
                }
                size="md"
                className="text-gray-500"
              />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.format && `${record.format.toUpperCase()} • `}
              {record.fileSize && `${(record.fileSize / 1024 / 1024).toFixed(2)} MB`}
              {record.duration && ` • ${Math.floor(record.duration / 60)}:${String(record.duration % 60).padStart(2, '0')}`}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('aiAgents:mediaSlideInForm.type'),
      dataIndex: 'type',
      width: '20%',
      render: (_, record) => (
        <span className="capitalize">{record.type}</span>
      ),
    },
    {
      key: 'createdAt',
      title: t('aiAgents:mediaSlideInForm.createdAt'),
      dataIndex: 'createdAt',
      width: '20%',
      render: (_, record) => (
        <span>{new Date(record.createdAt).toLocaleDateString('vi-VN')}</span>
      ),
    },
    {
      key: 'preview',
      title: t('aiAgents:mediaSlideInForm.preview'),
      width: '20%',
      render: (_, record) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            window.open(record.url, '_blank');
          }}
        >
          <Icon name="eye" size="sm" className="" />
         
        </Button>
      ),
    },
  ];

  // Kiểm tra có thay đổi chưa lưu không
  // useEffect(() => {
  //   const hasUnsavedChanges = mode === 'edit' && (
  //     selectedIds.length !== selectedMediaIds.length ||
  //     selectedIds.some(id => !selectedMediaIds.includes(id)) ||
  //     selectedMediaIds.some(id => !selectedIds.includes(id))
  //   );

  //   setHasChanges(hasUnsavedChanges || selectedIds.length > 0);
  // }, [selectedIds, selectedMediaIds, mode]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: SortDirection) => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: column,
      sortDirection: direction,
    }));
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentId) {
        validationError(t('aiAgents:mediaSlideInForm.cannotSaveInThisMode'));
        return;
      }

      setIsSubmitting(true);
      try {
        // Gọi API để cập nhật media cho agent
        await addMediaMutation.mutateAsync({
          agentId,
          data: { mediaIds: selectedIds }
        });

        updateSuccess(t('aiAgents:responseConfig.media'));

        onClose();
      } catch (error) {
        updateError(t('aiAgents:responseConfig.media'), error instanceof Error ? error.message : undefined);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách đã chọn
      const selectedMediaItems = mediaItems.filter(item => selectedIds.includes(item.id));

      if (onResourceAdded) {
        onResourceAdded(selectedMediaItems);
      }

      success({
        message: t('aiAgents:mediaSlideInForm.addMediaToListSuccess'),
      });

      onClose();
    }
  };

  // Xử lý đóng form - không cần confirm
  const handleClose = useCallback(() => {
    setQueryParams(prev => ({ ...prev, search: '' }));
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: t('aiAgents:mediaSlideInForm.sortBy'),
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: t('aiAgents:mediaSlideInForm.name'),
      onClick: () => handleSortChange('name', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'sort-date',
      label: t('aiAgents:mediaSlideInForm.createdAt'),
      onClick: () => handleSortChange('createdAt', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'sort-size',
      label: t('aiAgents:mediaSlideInForm.size'),
      onClick: () => handleSortChange('size', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: t('aiAgents:mediaSlideInForm.filterBy'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: t('aiAgents:mediaSlideInForm.all'),
      onClick: () => setQueryParams(prev => ({ ...prev, search: '' })),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:mediaSlideInForm.title')}</Typography>
         <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('aiAgents:mediaSlideInForm.cancel')}
            onClick={handleClose}
            disabled={isSubmitting}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('aiAgents:mediaSlideInForm.save')}
            onClick={handleSave}
            disabled={isLoading || isSubmitting || (mode === 'edit' && !agentId) || selectedIds.length === 0}
            isLoading={isSubmitting}
          />
         
        </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<Media>
            columns={columns}
            data={mediaItems}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== (queryParams.limit || 10)) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </div>

        {/* Nút lưu */}
       
      </Card>
    </SlideInForm>
  );
};

export default MediaSlideInForm;
