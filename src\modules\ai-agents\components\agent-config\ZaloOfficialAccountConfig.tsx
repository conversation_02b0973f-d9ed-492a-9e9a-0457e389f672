import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { Button, Icon, Typography, Modal } from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import ZaloSlideInForm, { ZaloOfficialAccount } from './ZaloSlideInForm';
import { useAgentZaloOfficialAccounts, useRemoveZaloOfficialAccounts } from '../../hooks/useAgentResources';

/**
 * Interface cho dữ liệu Zalo Official Account Config
 */
export interface ZaloOfficialAccountConfigData {
  zaloOfficialAccountIds: number[];
}

interface ZaloOfficialAccountConfigProps {
  initialData?: ZaloOfficialAccountConfigData;
  onSave?: (data: ZaloOfficialAccountConfigData) => void;
  mode?: 'create' | 'edit';
  agentId?: string | undefined;
}

/**
 * API function để lấy thông tin chi tiết của Zalo Official Accounts
 */
const getZaloOfficialAccountsByIds = async (
  ids: number[]
): Promise<ApiResponseDto<ZaloOfficialAccount[]>> => {
  if (ids.length === 0) {
    return { code: 200, message: 'Success', result: [] };
  }

  // Gọi API paginated và filter theo IDs (workaround vì chưa có endpoint by-ids)
  const response = await apiClient.get<{ items: ZaloOfficialAccount[] }>(`/marketing/zalo/paginated?limit=100`);
  const allAccounts = response.result.items || [];
  const filteredAccounts = allAccounts.filter((account: ZaloOfficialAccount) =>
    ids.includes(account.id)
  );

  return {
    code: 200,
    message: 'Success',
    result: filteredAccounts
  };
};

const ZaloOfficialAccountConfig: React.FC<ZaloOfficialAccountConfigProps> = ({
  initialData,
  onSave,
  mode = 'create',
  agentId
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    deleteSuccess,
    deleteError
  } = useAiAgentNotification();

  // State cho dữ liệu config
  const [configData, setConfigData] = useState<ZaloOfficialAccountConfigData>(
    initialData || {
      zaloOfficialAccountIds: [],
    }
  );

  // State cho form slide-in
  const [showZaloForm, setShowZaloForm] = useState(false);
  const [removingAccountId, setRemovingAccountId] = useState<number | null>(null);

  // State cho modal xác nhận xóa (chỉ dùng ở mode edit)
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<ZaloOfficialAccount | null>(null);

  // API hook để lấy dữ liệu từ agent khi ở mode edit
  const { data: agentZaloResponse } = useAgentZaloOfficialAccounts(agentId && mode === 'edit' ? agentId : '');
  const removeZaloMutation = useRemoveZaloOfficialAccounts();

  // Sử dụng ref để track việc đã initialize chưa
  const isInitialized = useRef(false);

  // Cập nhật dữ liệu khi initialData thay đổi hoặc khi load từ agent
  useEffect(() => {
    if (mode === 'edit' && agentZaloResponse?.result?.items) {
      const zaloIds = agentZaloResponse.result.items.map((item: { id: number }) => item.id);
      setConfigData({ zaloOfficialAccountIds: zaloIds });
      isInitialized.current = true;
    } else if (initialData && !isInitialized.current) {
      // Chỉ set initialData lần đầu tiên để tránh vòng lặp
      setConfigData(initialData);
      isInitialized.current = true;
    }
  }, [initialData, agentZaloResponse, mode]);

  // Query để lấy thông tin chi tiết của các accounts đã chọn
  const {
    data: selectedAccountsResponse,
    isLoading: isLoadingSelectedAccounts,
    refetch: refetchSelectedAccounts
  } = useQuery({
    queryKey: ['selected-zalo-accounts', configData.zaloOfficialAccountIds],
    queryFn: () => getZaloOfficialAccountsByIds(configData.zaloOfficialAccountIds),
    enabled: configData.zaloOfficialAccountIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const selectedAccounts = selectedAccountsResponse?.result || [];

  // Xử lý khi lưu từ ZaloSlideInForm
  const handleZaloFormSave = (selectedAccountIds: number[]) => {
    const newConfigData: ZaloOfficialAccountConfigData = {
      zaloOfficialAccountIds: selectedAccountIds,
    };

    setConfigData(newConfigData);

    // Gọi callback để cập nhật parent component
    if (onSave) {
      onSave(newConfigData);
    }

    // Refetch selected accounts để cập nhật UI
    if (selectedAccountIds.length > 0) {
      refetchSelectedAccounts();
    }
  };

  // Xử lý khi click vào icon xóa
  const handleRemoveClick = (account: ZaloOfficialAccount) => {
    if (mode === 'edit') {
      // Mode edit: Hiển thị modal xác nhận
      setAccountToDelete(account);
      setShowDeleteModal(true);
    } else {
      // Mode create: Xóa trực tiếp
      handleRemoveAccount(account.id);
    }
  };

  // Xử lý xóa account
  const handleRemoveAccount = async (accountId: number) => {
    if (mode === 'edit' && agentId) {
      // Mode edit: Gọi API để xóa khỏi agent
      setRemovingAccountId(accountId);
      try {
        await removeZaloMutation.mutateAsync({
          agentId,
          data: { zaloOfficialAccountIds: [accountId] }
        });

        deleteSuccess(t('aiAgents:zaloOfficialAccountConfig.title'));

        // Cập nhật local state
        const newAccountIds = configData.zaloOfficialAccountIds.filter(id => id !== accountId);
        const newConfigData: ZaloOfficialAccountConfigData = {
          zaloOfficialAccountIds: newAccountIds,
        };
        setConfigData(newConfigData);

      } catch (error) {
        deleteError(t('aiAgents:zaloOfficialAccountConfig.title'), error instanceof Error ? error.message : undefined);
      } finally {
        setRemovingAccountId(null);
      }
    } else {
      // Mode create: Chỉ cập nhật local state
      const newAccountIds = configData.zaloOfficialAccountIds.filter(id => id !== accountId);
      const newConfigData: ZaloOfficialAccountConfigData = {
        zaloOfficialAccountIds: newAccountIds,
      };

      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }

      deleteSuccess(t('aiAgents:zaloOfficialAccountConfig.title'));
    }
  };

  // Xử lý xác nhận xóa từ modal
  const handleConfirmDelete = async () => {
    if (accountToDelete) {
      await handleRemoveAccount(accountToDelete.id);
      setShowDeleteModal(false);
      setAccountToDelete(null);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setAccountToDelete(null);
  };

  // Format ngày tháng
  const formatDate = (timestamp: string) => {
    try {
      const date = new Date(parseInt(timestamp));
      return date.toLocaleDateString('vi-VN');
    } catch {
      return timestamp;
    }
  };

  return (
    <ConfigComponentWrapper
      componentId="zalo-official-account"

      title={
        <div className="flex items-center">
          <Icon name="zap" size="lg" className="mr-2 w-6 h-6 rounded-full bg-blue-600" />
          <span>{t('aiAgents:zaloOfficialAccountConfig.title')}</span>
        </div>
      }
        >
        <div className="space-y-4">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('aiAgents:zaloOfficialAccountConfig.title')}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('aiAgents:zaloOfficialAccountConfig.description')}
            </p>
          </div>

          {/* Header section */}
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
              <Icon name="zap" size="sm" className="text-white" />
            </div>
            <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
              {t('aiAgents:zaloOfficialAccountConfig.zaloAccounts')}
            </h3>
          </div>

          {/* Danh sách accounts đã chọn */}
          {isLoadingSelectedAccounts ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : selectedAccounts.length > 0 ? (
            <div className="space-y-3">
              <div className={`space-y-3 ${selectedAccounts.length > 10 ? 'max-h-96 overflow-y-auto pr-2' : ''}`}>
                {selectedAccounts.map((account) => (
                  <div
                    key={account.id}
                    className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      {/* Avatar */}
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                        {account.avatarUrl ? (
                          <img
                            src={account.avatarUrl}
                            alt={account.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Icon name="user" size="md" className="text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Account info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Typography variant="body2" className="font-semibold truncate text-gray-900 dark:text-gray-100">
                            {account.name}
                          </Typography>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${account.status === 'active'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                            }`}>
                            {account.status === 'active'
                              ? t('aiAgents:zaloOfficialAccountConfig.active')
                              : t('aiAgents:zaloOfficialAccountConfig.inactive')
                            }
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                          <div className="font-mono">OA ID: {account.oaId}</div>
                          <div>{t('aiAgents:zaloOfficialAccountConfig.createdAt')}: {formatDate(account.createdAt)}</div>
                        </div>
                      </div>
                    </div>

                    {/* Remove button */}
                    <div className="flex-shrink-0 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveClick(account)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 p-2 rounded-full transition-all duration-200"
                        disabled={removingAccountId === account.id}
                        title={t('aiAgents:zaloOfficialAccountConfig.removeAccount')}
                      >
                        {removingAccountId === account.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-500 border-t-transparent"></div>
                        ) : (
                          <Icon name="trash" size="sm" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
              <Typography variant="body2" className="text-gray-500 text-center mb-2">
                {t('aiAgents:zaloOfficialAccountConfig.noAccountsSelected')}
              </Typography>
            </div>
          )}

          {/* Nút thêm Zalo account */}
          {!showZaloForm && (
            <div className='flex justify-center pt-6'>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowZaloForm(true)}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                {t('aiAgents:zaloOfficialAccountConfig.addAccount')}
              </Button>
            </div>
          )}

          {/* Zalo Slide-in Form */}
          <ZaloSlideInForm
            isVisible={showZaloForm}
            onClose={() => setShowZaloForm(false)}
            onSave={handleZaloFormSave}
            agentId={agentId}
            mode={mode}
            initialSelectedIds={configData.zaloOfficialAccountIds}
          />

          {/* Modal xác nhận xóa - chỉ hiển thị ở mode edit */}
          {mode === 'edit' && (
            <Modal
              isOpen={showDeleteModal}
              onClose={handleCancelDelete}
              title={t('common:confirmDelete', 'Xác nhận xóa')}
              size="md"
              footer={
                <div className="flex justify-end space-x-3">
                  <Button variant="outline" onClick={handleCancelDelete}>
                    {t('common:cancel', 'Hủy')}
                  </Button>
                  <Button
                    variant="danger"
                    onClick={handleConfirmDelete}
                    isLoading={removingAccountId === accountToDelete?.id}
                    disabled={removingAccountId === accountToDelete?.id}
                  >
                    {t('common:delete', 'Xóa')}
                  </Button>
                </div>
              }
            >
              <div className="py-4">
                <Typography className="mb-4">
                  {t(
                    'aiAgents:zaloOfficialAccountConfig.confirmDeleteAccount',
                    'Bạn có chắc chắn muốn xóa tài khoản Zalo "{{accountName}}" khỏi Agent không?',
                    { accountName: accountToDelete?.name || '' }
                  )}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  {t(
                    'aiAgents:zaloOfficialAccountConfig.deleteAccountWarning',
                    'Hành động này không thể hoàn tác.'
                  )}
                </Typography>
              </div>
            </Modal>
          )}
        </div>
    </ConfigComponentWrapper >
  );
};

export default ZaloOfficialAccountConfig;
