import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '@/shared/store';
import { env } from '@/shared/utils';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

// Tạo instance axios với cấu hình mặc định
const axiosInstance = axios.create({
  baseURL: env.apiUrl + '/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor cho request
axiosInstance.interceptors.request.use(
  config => {
    const token = store.getState().authCommon.accessToken;
    console.log('Token:', token);
    if (token) {
      try {
        if (config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Error adding token to headers:', error);
      }
    } else {
      console.warn('No token available for request that requires authentication');
    }

    // Thêm language header (Accept-Language là standard header, không gây CORS issues)
    const language = localStorage.getItem('language') || 'vi';
    if (config.headers) {
      config.headers['Accept-Language'] = language;
    }

    // TODO: Thêm theme và country headers sau khi backend cấu hình CORS
    // const theme = localStorage.getItem('theme-mode') || 'light';
    // if (config.headers) {
    //   config.headers['X-Theme'] = theme;
    // }

    // const country = localStorage.getItem('country') || 'VN';
    // if (config.headers) {
    //   config.headers['X-Country'] = country;
    // }

    // Log headers sau khi xử lý
    console.log('🔍 Final request headers:', config.headers);
    console.log('🔍 Request URL:', config.url);
    console.log('🔍 Request method:', config.method);

    return config;
  },
  error => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Interceptor cho response
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Xử lý lỗi response
    if (error.response) {
      // Xử lý lỗi 401 Unauthorized
      // if (error.response.status === 401) {
      //   // Chuyển hướng đến trang đăng nhập
      //   // Token sẽ được xóa bởi component xử lý đăng xuất
      //   window.location.href = '/auth';
      // }

      // // Xử lý lỗi 403 Forbidden
      // if (error.response.status === 403) {
      //   // Chuyển hướng đến trang không có quyền truy cập
      //   window.location.href = '/forbidden';
      // }

      // Xử lý lỗi 500 Internal Server Error
      if (error.response.status === 500) {
        console.error('Server error:', error.response.data);
      }
    } else if (error.request) {
      // Xử lý lỗi không có response
      console.error('No response received:', error.request);
    } else {
      // Xử lý lỗi khác
      console.error('Error:', error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * Mở rộng AxiosRequestConfig để hỗ trợ chỉ định loại token
 */
export interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  /**
   * Loại token sử dụng cho request
   * - 'user': Sử dụng token (giữ lại để tương thích với code cũ)
   * - 'admin': Sử dụng token (giữ lại để tương thích với code cũ)
   * - 'none': Không sử dụng token
   *
   * Lưu ý: Token sẽ được tự động thêm vào request trừ khi tokenType là 'none'.
   */
  tokenType?: 'user' | 'admin' | 'none';
}

/**
 * Tạo config với token được chỉ định rõ ràng
 * @param config Config gốc
 * @returns Config đã được xử lý
 *
 * Lưu ý: Hàm này đã được đơn giản hóa. Token sẽ được tự động thêm vào request
 * trong interceptor, trừ khi tokenType là 'none'.
 */
const createConfigWithToken = (config?: ExtendedAxiosRequestConfig): AxiosRequestConfig => {
  if (!config) return {};

  // Trả về config gốc, token sẽ được xử lý trong interceptor
  return config;
};

// Hàm helper để gọi API
export const apiClient = {
  get: <T = unknown>(url: string, config?: ExtendedAxiosRequestConfig) =>
    axiosInstance
      .get<ApiResponseDto<T>>(url, createConfigWithToken(config))
      .then(response => response.data),

  post: <T = unknown>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig) =>
    axiosInstance
      .post<ApiResponseDto<T>>(url, data, createConfigWithToken(config))
      .then(response => response.data),

  put: <T = unknown>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig) =>
    axiosInstance
      .put<ApiResponseDto<T>>(url, data, createConfigWithToken(config))
      .then(response => response.data),

  patch: <T = unknown>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig) =>
    axiosInstance
      .patch<ApiResponseDto<T>>(url, data, createConfigWithToken(config))
      .then(response => response.data),

  delete: <T = unknown>(url: string, config?: ExtendedAxiosRequestConfig) =>
    axiosInstance
      .delete<ApiResponseDto<T>>(url, createConfigWithToken(config))
      .then(response => response.data),
};

export default axiosInstance;
