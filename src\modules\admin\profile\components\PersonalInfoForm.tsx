import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Select,
  Icon,
  Typography,
  PhoneInputWithCountry,
  DatePickerFormField,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FormStatus } from '../types/profile.types';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { GenderEnum } from '../types/user.types';
import { createPersonalInfoSchema, PersonalInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';

/**
 * Component form thông tin cá nhân
 */
const PersonalInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const { showNotification } = useProfileNotification();

  // Ref để truy cập form methods
  const formRef = useRef<FormRef<PersonalInfoSchema>>(null);

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const personalInfoSchema = createPersonalInfoSchema(t);

  // Dữ liệu mẫu thay vì gọi API
  const mockUser = {
    fullName: 'Nguyễn Văn Admin',
    gender: GenderEnum.MALE,
    dateOfBirth: '1990-01-01',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    email: '<EMAIL>',
    phoneNumber: '0123456789',
    isVerifyEmail: true,
    isVerifyPhone: true,
  };

  // Xử lý khi submit form - chỉ hiển thị thông báo thành công
  const onSubmit = (data: PersonalInfoSchema) => {
    // Cập nhật trạng thái form
    setFormStatus(FormStatus.SUBMITTING);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      setFormStatus(FormStatus.IDLE);
      showNotification(
        'success',
        t('profile:messages.updateSuccess', 'Cập nhật thông tin cá nhân thành công')
      );
      console.log('Form data:', data);
    }, 1000);
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e?: React.MouseEvent) => {
    // Ngăn chặn sự kiện mặc định (nếu có) để tránh gửi form
    if (e) {
      e.preventDefault();
    }

    // Reset form về giá trị ban đầu từ mock data
    if (formRef.current) {
      formRef.current.reset({
        fullName: mockUser.fullName,
        gender: mockUser.gender,
        dateOfBirth: mockUser.dateOfBirth,
        address: mockUser.address,
        email: mockUser.email,
        phoneNumber: mockUser.phoneNumber,
      });
    }
  };

  // Danh sách giới tính
  const genderOptions = [
    { value: GenderEnum.MALE, label: t('profile:personalInfo.genderOptions.male') },
    { value: GenderEnum.FEMALE, label: t('profile:personalInfo.genderOptions.female') },
    { value: GenderEnum.OTHER, label: t('profile:personalInfo.genderOptions.other') },
  ];

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="user" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:personalInfo.title')}
      </Typography>
    </div>
  );

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={personalInfoSchema}
        onSubmit={data => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            return;
          }
          onSubmit(data as PersonalInfoSchema);
        }}
        defaultValues={{
          fullName: mockUser.fullName,
          gender: mockUser.gender,
          dateOfBirth: mockUser.dateOfBirth,
          address: mockUser.address,
          email: mockUser.email,
          phoneNumber: mockUser.phoneNumber,
        }}
      >
        <div className="space-y-6">
          {/* Họ và tên */}
          <FormItem name="fullName" label={t('profile:personalInfo.fullName')} required>
            <Input placeholder={t('profile:personalInfo.fullName')} fullWidth />
          </FormItem>

          {/* Giới tính */}
          <FormItem name="gender" label={t('profile:personalInfo.gender')} required>
            <Select
              options={genderOptions}
              placeholder={t('profile:personalInfo.gender')}
              fullWidth
            />
          </FormItem>

          {/* Ngày sinh */}
          <FormItem name="dateOfBirth" label={t('profile:personalInfo.birthDate')}>
            <DatePickerFormField
              placeholder={t('profile:personalInfo.birthDate')}
              fullWidth
              stringFormat="yyyy-MM-dd"
            />
          </FormItem>

          {/* Địa chỉ */}
          <FormItem name="address" label={t('profile:personalInfo.address')} required>
            <Input placeholder={t('profile:personalInfo.address')} fullWidth />
          </FormItem>

          {/* Email */}
          <FormItem
            name="email"
            label={
              <div className="flex items-center">
                <span>{t('profile:personalInfo.email')}</span>
                {mockUser.isVerifyEmail && <Icon name="check" className="ml-2 text-green-500" />}
              </div>
            }
            required={!mockUser.isVerifyEmail}
          >
            <Input
              disabled={mockUser.isVerifyEmail}
              placeholder={t('profile:personalInfo.email')}
              type="email"
              fullWidth
            />
          </FormItem>

          {/* Số điện thoại */}
          <FormItem
            name="phoneNumber"
            label={
              mockUser.isVerifyPhone ? (
                <div className="flex items-center">
                  <span>{t('profile:personalInfo.phone')}</span>
                  <Icon name="check" className="ml-2 text-green-500" />
                </div>
              ) : (
                t('profile:personalInfo.phone')
              )
            }
            required={!mockUser.isVerifyPhone}
          >
            <PhoneInputWithCountry
              disabled={Boolean(mockUser && mockUser.isVerifyPhone)}
              placeholder={t('profile:personalInfo.phone')}
              fullWidth
              defaultCountry="VN"
            />
          </FormItem>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              type="button"
              onClick={e => handleCancel(e)}
              disabled={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.cancel')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.save')}
            </Button>
          </div>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default PersonalInfoForm;
