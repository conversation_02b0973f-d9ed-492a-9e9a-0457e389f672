import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useAdminTypeAgentsTrash } from '../agent-type/hooks/useTypeAgent';
import { TypeAgentListItem, AgentTypeStatusEnum } from '../agent-type/types/type-agent.types';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentTypeCard from '../components/AdminAgentTypeCard';

// Interface cho dữ liệu từ API trash
interface ApiTypeAgentTrashItem {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

// Interface cho response từ API trash
interface ApiTypeAgentTrashResponse {
  items: ApiTypeAgentTrashItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Trang hiển thị danh sách Type Agents đã xóa
 */
const AgentTypeDeletePage: React.FC = () => {
  const { t } = useTranslation(['admin-agent']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || '',
  };

  // Lấy danh sách type agents đã xóa
  const { data: typesResponse, isLoading, error, refetch } = useAdminTypeAgentsTrash(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleBackToMain = () => {
    navigate('/admin/agent/types');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const types = useMemo(() => {
    const response = typesResponse as ApiTypeAgentTrashResponse | undefined;
    if (!response?.items) {
      return [];
    }

    const apiTypes = response.items;

    return apiTypes.map((apiType: ApiTypeAgentTrashItem): TypeAgentListItem => ({
      id: apiType.id,
      name: apiType.name,
      description: apiType.description,
      createdAt: apiType.createdAt,
      status: apiType.status as AgentTypeStatusEnum,
    }));
  }, [typesResponse]);

  const totalItems = (typesResponse as ApiTypeAgentTrashResponse | undefined)?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          items={[]}
          additionalIcons={[
            {
              icon: 'arrow-left',
              tooltip: t('admin-agent:type.backToMain', 'Back to Main List'),
              variant: 'default',
              onClick: handleBackToMain,
              className: 'text-blue-600 hover:text-blue-800',
            }
          ]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          items={[]}
          additionalIcons={[
            {
              icon: 'arrow-left',
              tooltip: t('admin-agent:type.backToMain', 'Back to Main List'),
              variant: 'default',
              onClick: handleBackToMain,
              className: 'text-blue-600 hover:text-blue-800',
            }
          ]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('admin-agent:common.error', 'Error')}
          description={t('admin-agent:list.loadError', 'Unable to load agent type list. Please try again.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('admin-agent:common.refresh', 'Refresh')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        items={[]}
        additionalIcons={[
          {
            icon: 'arrow-left',
            tooltip: t('admin-agent:type.backToMain', 'Back to Main List'),
            variant: 'default',
            onClick: handleBackToMain,
            className: 'text-blue-600 hover:text-blue-800',
          }
        ]}
      />

      {types.length > 0 ? (
        <>
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {types.map(type => (
              <div key={type.id} className="h-full">
                <AdminAgentTypeCard
                  type={type}
                  allTypes={types}
                  isTrashPage={true}
                  onSuccess={refetch}
                />
              </div>
            ))}
          </ResponsiveGrid>

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="trash"
          title={t('admin-agent:trash.noAgents', 'No agent types in trash')}
          description={
            search
              ? t('admin-agent:type.noSearchResults', 'No agent types found matching the search criteria.')
              : t('admin-agent:trash.noAgentsDescription', 'Trash is empty. Deleted agent types will appear here.')
          }
          actions={
            <Button
              variant="outline"
              onClick={handleBackToMain}
            >
              {t('admin-agent:type.backToMain', 'Back to Main List')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentTypeDeletePage;
