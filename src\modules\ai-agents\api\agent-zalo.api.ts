import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

// Types cho Zalo Official Account
export interface ZaloOfficialAccountDto {
  id: number;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface AgentZaloOfficialAccountsResponse {
  items: ZaloOfficialAccountDto[];
  totalItems: number;
}

export interface AddZaloOfficialAccountsDto {
  zaloOfficialAccountIds: number[];
}

export interface RemoveZaloOfficialAccountsDto {
  zaloOfficialAccountIds: number[];
}

// API functions
export const getAgentZaloOfficialAccounts = async (
  agentId: string
): Promise<ApiResponseDto<AgentZaloOfficialAccountsResponse>> => {
  return apiClient.get(`/user/agents/${agentId}/zalo-official-accounts`);
};

export const addZaloOfficialAccountsToAgent = async (
  agentId: string,
  data: AddZaloOfficialAccountsDto
): Promise<ApiResponseDto<void>> => {
  return apiClient.post(`/user/agents/${agentId}/zalo-official-accounts`, data);
};

export const removeZaloOfficialAccountsFromAgent = async (
  agentId: string,
  data: RemoveZaloOfficialAccountsDto
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/zalo-official-accounts`, { data });
};
