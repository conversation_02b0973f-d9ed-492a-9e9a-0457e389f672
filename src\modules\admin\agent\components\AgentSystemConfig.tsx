import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { Button, Icon, Typography, Modal } from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import AgentSystemSlideInForm, { AgentSystem } from './AgentSystemSlideInForm';

/**
 * Interface cho dữ liệu Agent System Config
 */
export interface AgentSystemConfigData {
  agentSystems: string[];
}

interface AgentSystemConfigProps {
  initialData?: AgentSystemConfigData;
  onSave?: (data: AgentSystemConfigData) => void;
  mode?: 'create' | 'edit';
  agentTypeId?: string | undefined;
}

/**
 * API function để lấy thông tin chi tiết của Agent Systems
 */
const getAgentSystemsByIds = async (
  ids: string[]
): Promise<ApiResponseDto<AgentSystem[]>> => {
  if (ids.length === 0) {
    return { code: 200, message: 'Success', result: [] };
  }

  // Gọi API để lấy tất cả agent systems và filter theo IDs
  const response = await apiClient.get<{ items: AgentSystem[] }>(`/admin/agents/system?limit=100`);
  const allSystems = response.result.items || [];
  const filteredSystems = allSystems.filter((system: AgentSystem) =>
    ids.includes(system.id)
  );

  return {
    code: 200,
    message: 'Success',
    result: filteredSystems
  };
};

const AgentSystemConfig: React.FC<AgentSystemConfigProps> = ({
  initialData,
  onSave,
  mode = 'create',
  agentTypeId
}) => {
   const { t } = useTranslation(['admin', 'common']);
   const {
     deleteSuccess,
     deleteError
   } = useAdminAgentNotification();

  // State cho dữ liệu config
  const [configData, setConfigData] = useState<AgentSystemConfigData>(
    initialData || {
      agentSystems: [],
    }
  );

  // State cho form slide-in
  const [showSystemForm, setShowSystemForm] = useState(false);
  const [removingSystemId, setRemovingSystemId] = useState<string | null>(null);

  // State cho modal xác nhận xóa (chỉ dùng ở mode edit)
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [systemToDelete, setSystemToDelete] = useState<AgentSystem | null>(null);

  // Cập nhật dữ liệu khi initialData thay đổi
  useEffect(() => {
    if (initialData) {
      setConfigData(initialData);
    }
  }, [initialData]);

  // Query để lấy thông tin chi tiết của các systems đã chọn
  const {
    data: selectedSystemsResponse,
    isLoading: isLoadingSelectedSystems,
    refetch: refetchSelectedSystems
  } = useQuery({
    queryKey: ['selected-agent-systems', configData.agentSystems],
    queryFn: () => getAgentSystemsByIds(configData.agentSystems),
    enabled: configData.agentSystems.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const selectedSystems = selectedSystemsResponse?.result || [];

  // Xử lý khi lưu từ AgentSystemSlideInForm
  const handleSystemFormSave = (selectedSystemIds: string[]) => {
    const newConfigData: AgentSystemConfigData = {
      agentSystems: selectedSystemIds,
    };

    setConfigData(newConfigData);

    // Gọi callback để cập nhật parent component
    if (onSave) {
      onSave(newConfigData);
    }

    // Refetch selected systems để cập nhật UI
    if (selectedSystemIds.length > 0) {
      refetchSelectedSystems();
    }
  };

  // Xử lý khi click vào icon xóa
  const handleRemoveClick = (system: AgentSystem) => {
    if (mode === 'edit') {
      // Mode edit: Hiển thị modal xác nhận
      setSystemToDelete(system);
      setShowDeleteModal(true);
    } else {
      // Mode create: Xóa trực tiếp
      handleRemoveSystem(system.id);
    }
  };

  // Xử lý xóa system
  const handleRemoveSystem = async (systemId: string) => {
    if (mode === 'edit' && agentTypeId) {
      // Mode edit: Gọi API để xóa khỏi agent type (nếu cần)
      setRemovingSystemId(systemId);
      try {
        // Cập nhật local state
        const newSystemIds = configData.agentSystems.filter(id => id !== systemId);
        const newConfigData: AgentSystemConfigData = {
          agentSystems: newSystemIds,
        };
        setConfigData(newConfigData);

        deleteSuccess('Agent System');

      } catch (error) {
        deleteError('Agent System', error instanceof Error ? error.message : undefined);
      } finally {
        setRemovingSystemId(null);
      }
    } else {
      // Mode create: Chỉ cập nhật local state
      const newSystemIds = configData.agentSystems.filter(id => id !== systemId);
      const newConfigData: AgentSystemConfigData = {
        agentSystems: newSystemIds,
      };

      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }

      deleteSuccess('Agent System');
    }
  };

  // Xử lý xác nhận xóa từ modal
  const handleConfirmDelete = async () => {
    if (systemToDelete) {
      await handleRemoveSystem(systemToDelete.id);
      setShowDeleteModal(false);
      setSystemToDelete(null);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setSystemToDelete(null);
  };

  // Format ngày tháng


  return (
    <div className="space-y-4">
      {/* Nút thêm system */}
      <div className="flex justify-between items-center">
        <Typography variant="body2" className="text-gray-600 dark:text-gray-300">
          {configData.agentSystems.length > 0
            ? t('admin:agent.form.agentSystemsConfig.systemCount', 'Đã chọn {{count}} agent system', { count: configData.agentSystems.length })
            : t('admin:agent.form.agentSystemsConfig.noSystemsSelected', 'Chưa có agent system nào được chọn')
          }
        </Typography>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowSystemForm(true)}
        >
          <Icon name="plus" size="sm" className="mr-1" />
          {t('admin:agent.form.agentSystemsConfig.addSystem', 'Thêm Agent System')}
        </Button>
      </div>

      {/* Danh sách systems đã chọn */}
      {isLoadingSelectedSystems ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : selectedSystems.length > 0 ? (
        <div className="space-y-3">
          <Typography variant="body2" className="font-medium text-gray-700 dark:text-gray-300">
            {t('admin:agent.form.agentSystemsConfig.selectedSystems')}:
          </Typography>
          {selectedSystems.map((system) => (
            <div
              key={system.id}
              className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                {/* Icon */}
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex-shrink-0 flex items-center justify-center">
                  <Icon name="settings" size="md" className="text-white" />
                </div>

                {/* System info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Typography variant="body2" className="font-semibold truncate text-gray-900 dark:text-gray-100">
                      {system.name}
                    </Typography>
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <div>{system.nameCode}</div>
                  </div>
                </div>
              </div>

              {/* Remove button */}
              <div className="flex-shrink-0 ml-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveClick(system)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 p-2 rounded-full transition-all duration-200"
                  disabled={removingSystemId === system.id}
                  title={t('admin:agent.form.agentSystemsConfig.removeSystem')}
                >
                  {removingSystemId === system.id ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-500 border-t-transparent"></div>
                  ) : (
                    <Icon name="trash" size="sm" />
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <Typography variant="body2" className="text-gray-500 text-center mb-2">
            {t('admin:agent.form.agentSystemsConfig.noSystemsSelected')}
          </Typography>
        </div>
      )}

      {/* Agent System Slide-in Form */}
      <AgentSystemSlideInForm
        isVisible={showSystemForm}
        onClose={() => setShowSystemForm(false)}
        onSave={handleSystemFormSave}
        agentTypeId={agentTypeId}
        mode={mode}
        initialSelectedIds={configData.agentSystems}
      />

      {/* Modal xác nhận xóa - chỉ hiển thị ở mode edit */}
      {mode === 'edit' && (
        <Modal
          isOpen={showDeleteModal}
          onClose={handleCancelDelete}
          title={t('admin:agent.common.confirmDelete', 'Xác nhận xóa')}
          size="md"
          footer={
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancelDelete}>
                {t('admin:agent.type.common.cancel', 'Hủy')}
              </Button>
              <Button
                variant="danger"
                onClick={handleConfirmDelete}
                isLoading={removingSystemId === systemToDelete?.id}
                disabled={removingSystemId === systemToDelete?.id}
              >
                {t('admin:agent.type.common.delete', 'Xóa')}
              </Button>
            </div>
          }
        >
          <div className="py-4">
            <Typography className="mb-4">
              {t(
                'admin:agent.form.agentSystemsConfig.confirmDeleteSystem',
                'Bạn có chắc chắn muốn xóa agent system "{{systemName}}" khỏi loại agent không?',
                { systemName: systemToDelete?.name || '' }
              )}
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {t(
                'admin:agent.form.agentSystemsConfig.deleteSystemWarning',
                'Hành động này không thể hoàn tác.'
              )}
            </Typography>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default AgentSystemConfig;
