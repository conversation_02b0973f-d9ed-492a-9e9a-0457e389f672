/**
 * Enum cho trạng thái agent type
 */
export enum AgentTypeStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
}

/**
 * Enum cho các trường sắp xếp
 */
export enum TypeAgentSortBy {
  ID = 'id',
  NAME = 'name',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  employeeId: string;
  name: string;
  avatar: string | null;
  date?: Date;
}

/**
 * Interface cho cấu hình mặc định của agent type
 */
export interface DefaultConfig {
  enableAgentProfileCustomization: boolean;
  enableOutputToMessenger: boolean;
  enableOutputToWebsiteLiveChat: boolean;
  enableTaskConversionTracking: boolean;
  enableResourceUsage: boolean;
  enableDynamicStrategyExecution: boolean;
  enableMultiAgentCollaboration: boolean;
  enableOutputToZaloOA: boolean;
}

/**
 * Interface cho agent system trong danh sách
 */
export interface AgentSystemListItem {
  id: string;
  name: string;
  nameCode: string;
  avatar: string | null;
  model: string;
  active: boolean;
  provider: string;
  isSupervisor: boolean;
}

/**
 * Interface cho type agent trong danh sách
 */
export interface TypeAgentListItem {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: AgentTypeStatusEnum;
}

/**
 * Interface cho thông tin chi tiết type agent
 */
export interface TypeAgentDetail {
  id: number;
  name: string;
  description: string;
  defaultConfig: DefaultConfig;
  status: AgentTypeStatusEnum;
  agentSystems: string[];
  created?: EmployeeInfo;
  updated?: EmployeeInfo;
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo type agent
 */
export interface CreateTypeAgentParams {
  name: string;
  description: string;
  defaultConfig: DefaultConfig;
  status: AgentTypeStatusEnum;
  agentSystems: string[];
}

/**
 * Interface cho tham số cập nhật type agent
 */
export interface UpdateTypeAgentParams {
  name?: string;
  description?: string;
  defaultConfig?: DefaultConfig;
  status?: AgentTypeStatusEnum;
  agentSystems?: string[];
}

/**
 * Interface cho tham số query
 */
export interface TypeAgentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: AgentTypeStatusEnum;
  sortBy?: TypeAgentSortBy;
  sortDirection?: SortDirection;
}

/**
 * Interface cho response API
 */
export interface TypeAgentResponse {
  code: number;
  message: string;
  result: {
    items: TypeAgentListItem[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  };
}

/**
 * Interface cho response tạo type agent
 */
export interface CreateTypeAgentResponse {
  code: number;
  message: string;
  result: {
    id: number;
  };
}
