{"aiAgents": {"notification": {"createSuccess": "{{entityName}} has been created successfully", "updateSuccess": "{{entityName}} has been updated successfully", "deleteSuccess": "{{entityName}} has been deleted successfully", "copySuccess": "{{entityName}} has been copied successfully", "shareSuccess": "{{entityName}} has been shared successfully", "createError": "An error occurred while creating {{entityName}}", "updateError": "An error occurred while updating {{entityName}}", "deleteError": "An error occurred while deleting {{entityName}}", "copyError": "An error occurred while copying {{entityName}}", "shareError": "An error occurred while sharing {{entityName}}", "loadError": "Unable to load {{entityName}} list", "uploadSuccess": "Upload successful", "uploadSuccessWithName": "{{fileName}} uploaded successfully", "uploadError": "An error occurred while uploading", "validationError": "Invalid data", "permissionError": "You do not have permission to perform this action", "networkError": "Network connection error. Please try again", "processing": "{{action}}...", "saveSuccess": "Saved successfully", "saveError": "An error occurred while saving", "publishSuccess": "{{entityName}} has been published successfully", "publishError": "An error occurred while publishing {{entityName}}", "restoreSuccess": "{{entityName}} has been restored successfully", "restoreError": "An error occurred while restoring {{entityName}}"}, "common": {"save": "Save", "cancel": "Cancel", "add": "Add", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "required": "Required", "update": "Update", "create": "Create", "select": "Select", "configure": "Configure", "name": "Name", "description": "Description", "type": "Type", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "noData": "No data", "loading": "Loading...", "error": "An error occurred", "success": "Success", "confirm": "Confirm", "confirmDelete": "Are you sure you want to delete?", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "agentDetail": "Agent <PERSON><PERSON>"}, "aiAgents": "AI Agents", "agentCreate": {"title": "Create Agent", "customAgentButton": "Create Custom Agent", "selectAgentType": "Select Agent Type", "selectAgentDescription": "Choose an Agent type from the list below or create your own custom Agent.", "configureAgent": "Configure {name}", "sortBy": "Sort by", "sortName": "Name", "sortDate": "Created Date", "order": "Order", "orderAsc": "Ascending", "orderDesc": "Descending", "agentTypeDescription": "Choose the agent type that suits your needs. Each agent type has different capabilities and characteristics."}, "integrationConfig": {"title": "Integrations", "facebook": "Facebook", "website": "Website", "addFacebook": "Add Facebook Integration", "addWebsite": "Add Website Integration", "noFacebookIntegration": "No Facebook integrations yet", "noWebsiteIntegration": "No Website integrations yet", "selectFacebook": "Select Facebook Page", "selectWebsite": "Select Website", "facebookPageName": "Facebook Page Name", "websiteName": "Website Name", "websiteUrl": "Website URL"}, "strategyConfig": {"title": "Strategy", "selectStrategy": "Select a strategy for your Agent", "selectStrategyDescription": "Choose a strategy and configure the processing steps", "basicStrategy": "Basic Strategy", "basicStrategyDescription": "Simple strategy with default settings", "advancedStrategy": "Advanced Strategy", "advancedStrategyDescription": "Strategy with advanced options and complex processing", "customStrategy": "Custom Strategy", "customStrategyDescription": "Create your own strategy with fully customized settings", "configureStrategy": "Configure Strategy", "step": "Step {number}", "input": "Input"}, "convertConfig": {"title": "Convert Configuration", "configureFields": "Configure data fields to collect", "configureFieldsDescription": "Select the data fields that the Agent will collect from users", "noFields": "No data fields configured yet", "addField": "Add New Field", "editField": "Edit Field", "fieldName": "Field Name", "fieldDescription": "Description", "fieldType": "Data Type", "fieldRequired": "Required Field", "fieldNamePlaceholder": "Enter field name (e.g., email)", "fieldDescriptionPlaceholder": "Enter field description (e.g., Collect all user emails)", "pleaseEnterAllFields": "Please enter all field information", "text": "Text", "email": "Email", "phone": "Phone", "number": "Number", "date": "Date", "address": "Address", "name": "Name"}, "responseConfig": {"title": "Response Resources", "configureResources": "Configure response resources for the Agent", "configureResourcesDescription": "Select the resources that the Agent can use to respond to users", "media": "Media Resources", "url": "URL Resources", "product": "Product Resources", "noMedia": "No Media resources yet", "noUrl": "No URL resources yet", "noProduct": "No Product resources yet", "selectMedia": "Select Media", "selectUrl": "Select URL", "selectProduct": "Select Product"}, "editAgent": {"title": "Edit Agent", "subtitle": "Update Agent information and configuration", "basicInfo": "Basic Information", "agentName": "Agent Name", "agentNamePlaceholder": "Enter Agent name", "agentDescription": "Agent Description", "agentDescriptionPlaceholder": "Enter Agent description", "agentAvatar": "Avatar", "changeAvatar": "Change Avatar", "agentStatus": "Status", "active": "Active", "inactive": "Inactive", "agentLevel": "Level", "agentExp": "Experience", "agentModel": "AI Model", "agentType": "Agent Type", "saveChanges": "Save Changes", "cancelEdit": "Cancel Edit", "deleteAgent": "Delete Agent", "confirmDelete": "Are you sure you want to delete this <PERSON>?", "deleteWarning": "This action cannot be undone.", "updateSuccess": "Agent updated successfully!", "updateError": "Failed to update <PERSON>!", "deleteSuccess": "Agent deleted successfully!", "deleteError": "Failed to delete Agent!", "validation": {"nameRequired": "Agent name is required", "nameMinLength": "Agent name must be at least 2 characters", "nameMaxLength": "Agent name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters"}}, "mediaSlideInForm": {"title": "Select Media", "close": "Close", "cancel": "Cancel", "save": "Save", "media": "Media", "type": "Type", "createdAt": "Created Date", "preview": "Preview", "view": "View", "sortBy": "Sort by", "name": "Name", "size": "Size", "filterBy": "Filter by", "all": "All", "cannotSaveInThisMode": "Cannot save in this mode.", "updateMediaSuccess": "Media updated successfully!", "updateMediaError": "An error occurred while updating media.", "addMediaToListSuccess": "Media added to list!", "loadingFacebookPages": "Loading Facebook Pages...", "errorLoadingFacebookPages": "Error loading Facebook Pages", "noFacebookIntegrations": "No Facebook integrations yet", "loadingWebsites": "Loading Websites...", "errorLoadingWebsites": "Error loading Websites", "noWebsiteIntegrations": "No Website integrations yet"}, "facebookSlideInForm": {"title": "Select Facebook Page", "close": "Close", "cancel": "Cancel", "save": "Save", "facebookPage": "Facebook Page", "category": "Category", "followers": "Followers", "status": "Status", "connected": "Connected", "notConnected": "Not Connected", "sortBy": "Sort by", "name": "Name", "filterBy": "Filter by", "all": "All", "cannotSaveInThisMode": "Cannot save in this mode.", "integratedSuccessfully": "Successfully integrated {{count}} Facebook Page!", "cannotIntegrate": "{{count}} Facebook Page cannot be integrated: {{details}}", "noPageIntegratedSuccessfully": "No Facebook Page was integrated successfully!", "updateIntegrationError": "An error occurred while updating Facebook integration.", "selectedFacebookIntegration": "Selected Facebook integration!", "connectedPages": "Connected", "notConnectedPages": "Not Connected"}, "websiteSlideInForm": {"title": "Select Website", "close": "Close", "cancel": "Cancel", "save": "Save", "website": "Website", "category": "Category", "status": "Status", "connected": "Connected", "processing": "Processing", "connectionError": "Connection Error", "notConnected": "Not Connected", "sortBy": "Sort by", "host": "Host", "createdDate": "Created Date", "filterBy": "Filter by", "all": "All", "verified": "Verified", "notVerified": "Not Verified", "addNewWebsite": "Add new website", "websiteName": "Website Name", "url": "URL", "enterWebsiteName": "Enter website name", "enterWebsiteUrl": "https://example.com", "add": "Add", "cannotSaveInThisMode": "Cannot save in this mode.", "integratedSuccessfully": "Successfully integrated {{count}} Website!", "cannotIntegrate": "{{count}} Website cannot be integrated: {{details}}", "noWebsiteIntegratedSuccessfully": "No Website was integrated successfully!", "updateIntegrationError": "An error occurred while updating Website integration.", "selectedWebsiteIntegration": "Selected Website integration!", "pleaseEnterAllInfo": "Please enter complete website information", "invalidUrl": "Invalid URL. Please enter a valid URL format (e.g., https://example.com)", "addWebsiteSuccess": "Website added successfully!", "addWebsiteError": "An error occurred while adding website"}, "integration": {"title": "Integrations", "connectAgentToPlatforms": "Connect Agent to platforms", "connectDescription": "Connect Agent to Facebook pages and Websites to interact with users", "connectAgentToWebsite": "Connect Agent to Website", "websiteDescription": "Connect Agent to Websites to interact with users", "connectAgentToFacebook": "Connect Agent to Facebook", "facebookDescription": "Connect Agent to Facebook pages to interact with users", "facebookIntegration": "Facebook Integration", "websiteIntegration": "Website Integration", "facebookPages": "Facebook Pages", "websites": "Websites", "add": "Add Website", "confirmDeleteIntegration": "Are you sure you want to remove the integration \"{{integrationName}}\" from the Agent?", "deleteIntegrationWarning": "This action cannot be undone.", "removeFacebookSuccess": "Facebook integration removed successfully!", "removeFacebookError": "An error occurred while removing Facebook integration.", "removeWebsiteSuccess": "Website integration removed successfully!", "removeWebsiteError": "An error occurred while removing Website integration."}, "multiAgent": {"title": "Multi-Agent Configuration", "description": "Configure multiple agents to work together. Each agent will have separate roles and functions.", "addAgent": "Add Agent", "agentList": "Agent List", "noAgents": "No agents yet", "addFirstAgent": "Add the first agent to start multi-agent configuration", "moveUp": "Move up", "moveDown": "Move down", "editDescription": "Edit description", "deleteAgent": "Delete agent", "save": "Save", "cancel": "Cancel", "enterDescription": "Enter description for agent...", "confirmDeleteAgent": "Are you sure you want to remove agent \"{{agent<PERSON><PERSON>}}\"?", "deleteAgentWarning": "This action cannot be undone.", "addNewAgent": "Add new Agent", "selectAgentType": "Select Agent Type", "selectAgent": "Select an agent...", "customDescription": "Custom description (optional)", "enterCustomDescription": "Enter custom description for this agent...", "useDefaultDescription": "If left empty, the default description of the agent type will be used", "promptForAgent": "Prompt for agent", "enterPrompt": "Enter prompt for this agent...", "required": "*"}, "agentCreatePage": {"title": "Create Agent", "selectAgentType": "Select Agent Type", "agentTypeDescription": "Choose the agent type that suits your needs. Each agent type has different capabilities and characteristics.", "allTypes": "All Types", "systemAgents": "System Agents", "userAgents": "User Agents", "sortByName": "Sort by Name", "sortByDate": "Sort by Date", "ascending": "Ascending", "descending": "Descending", "resetFilters": "Reset Filters", "errorLoadingAgents": "An error occurred while loading Type Agent list", "retry": "Retry"}, "modelConfig": {"title": "Model Configuration", "provider": "Provider", "keyLlm": "Key LLM", "selectKeyLlm": "Select Key LLM", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "instruction": "Instruction", "instructionPlaceholder": "Enter instructions for the model...", "advancedSettings": "Advanced Settings", "maxTokens": "<PERSON>", "maxTokensDescription": "Maximum number of tokens per API call", "temperature": "Temperature", "temperatureDescription": "Level of randomness in results (0-2)", "topP": "Top P", "topPDescription": "Cumulative probability for token selection (0-1)", "topK": "Top K", "topKDescription": "Number of highest probability tokens to consider", "loading": "Loading...", "errorLoadingModel": "Error loading model", "noModel": "No model available"}, "customToolConfig": {"title": "Tool Configuration", "description": "Select tools that the Agent can use", "availableTools": "Available Tools", "selectedTools": "Selected Tools", "noToolsSelected": "No tools selected", "selectTools": "Select Tools", "toolName": "Tool Name", "toolDescription": "Description", "addTool": "<PERSON><PERSON>", "removeTool": "Remove <PERSON>"}, "toolSlideInForm": {"title": "Select Tools", "close": "Close", "cancel": "Cancel", "save": "Save", "tool": "Tool", "description": "Description", "category": "Category", "status": "Status", "active": "Active", "inactive": "Inactive", "sortBy": "Sort by", "name": "Name", "filterBy": "Filter by", "all": "All", "updateToolsSuccess": "Tools updated successfully!", "updateToolsError": "An error occurred while updating tools.", "addToolsToListSuccess": "Tools added to list!"}, "profileConfig": {"title": "Profile Information", "name": "Name", "birthDate": "Birth Date", "gender": "Gender", "language": "Language", "education": "Education", "country": "Country", "position": "Position", "skills": "Skills", "personality": "Personality", "avatar": "Avatar", "male": "Male", "female": "Female", "other": "Other", "highSchool": "High School", "college": "College", "university": "University", "postgraduate": "Postgraduate", "addSkill": "<PERSON><PERSON>", "addPersonality": "Add Personality", "skillPlaceholder": "Enter label and press enter", "personalityPlaceholder": "Enter label and press enter", "namePlaceholder": "Enter agent name", "positionPlaceholder": "Enter position", "personalityTextPlaceholder": "Enter personality description"}, "agentConfigurationForm": {"createAgent": "Create Agent", "editAgent": "Edit Agent"}, "resourcesConfig": {"title": "Resources Configuration", "description": "Select resources that the Agent can use", "media": "Media", "urls": "URLs", "products": "Products", "addMedia": "Add Media", "addUrl": "Add URL", "addProduct": "Add Product", "noMediaSelected": "No media selected", "noUrlsSelected": "No URLs selected", "noProductsSelected": "No products selected", "selectedItems": "{{count}} items selected"}, "urlSlideInForm": {"title": "Title", "close": "Close", "cancel": "Cancel", "save": "Save", "url": "URL", "description": "Description", "category": "Category", "status": "Status", "lastChecked": "Last Checked", "active": "Active", "broken": "Broken", "pending": "Pending", "sortBy": "Sort by", "filterBy": "Filter by", "all": "All", "updateUrlsSuccess": "URLs updated successfully!", "updateUrlsError": "An error occurred while updating URLs.", "addUrlsToListSuccess": "URLs added to list!"}, "productSlideInForm": {"title": "Select Products", "close": "Close", "cancel": "Cancel", "save": "Save", "product": "Product", "name": "Name", "price": "Price", "category": "Category", "status": "Status", "stock": "Stock", "inStock": "In Stock", "outOfStock": "Out of Stock", "discontinued": "Discontinued", "sortBy": "Sort by", "filterBy": "Filter by", "all": "All", "updateProductsSuccess": "Products updated successfully!", "updateProductsError": "An error occurred while updating products.", "addProductsToListSuccess": "Products added to list!"}, "agentEditPage": {"title": "Edit Agent", "loading": "Loading agent information...", "notFound": "Agent not found", "error": "An error occurred while loading agent information"}, "zaloOfficialAccountConfig": {"title": "Zalo Official Account", "description": "Select Zalo Official Accounts to integrate with your agent", "addAccount": "<PERSON>d <PERSON> Account", "zaloAccounts": "<PERSON><PERSON> Accounts", "noAccountsSelected": "No accounts selected", "selectedAccounts": "Selected Accounts", "accountCount": "{{count}} accounts", "selectAccounts": "Select Zalo Accounts", "searchPlaceholder": "Search accounts...", "oaId": "OA ID", "name": "Name", "status": "Status", "createdAt": "Created At", "avatar": "Avatar", "active": "Active", "inactive": "Inactive", "selectAll": "Select All", "deselectAll": "Deselect All", "selected": "Selected", "save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "Error loading account list", "retry": "Retry", "noAccountsFound": "No accounts found", "updateAccountsSuccess": "Zalo accounts updated successfully!", "updateAccountsError": "Error updating Zalo accounts.", "addAccountsToListSuccess": "Zalo accounts added to list!", "removeAccount": "Remove account", "removeAccountSuccess": "Zalo account removed successfully!", "removeAccountError": "Error removing <PERSON><PERSON> account.", "confirmDeleteAccount": "Are you sure you want to remove <PERSON>alo account \"{{accountName}}\" from the Agent?", "deleteAccountWarning": "This action cannot be undone."}, "zaloSlideInForm": {"title": "Select Zalo Official Accounts", "close": "Close", "account": "Account", "status": "Status", "active": "Active", "inactive": "Inactive", "createdAt": "Created At", "filterBy": "Filter <PERSON>", "all": "All", "cancel": "Cancel", "save": "Save", "addAccountsToListSuccess": "Zalo accounts added successfully!", "updateAccountsSuccess": "Zalo accounts updated successfully!", "updateAccountsError": "Error updating Zalo accounts.", "cannotSaveInThisMode": "Cannot save in this mode"}}}