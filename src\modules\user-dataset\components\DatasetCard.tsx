import React, { useState } from 'react';
import { Card, Chip, IconCard, Tooltip, ConfirmDeleteModal } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  UserDataFineTuneResponseDto,
  DataFineTuneStatus,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useDeleteUserDataFineTune } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { useSmartNotification } from '@/shared';

interface DatasetCardProps {
  dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum };
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  className?: string;
}

const DatasetCard: React.FC<DatasetCardProps> = ({
  dataset,
  onClick,
  onDelete,
  className = '',
}) => {
  const { t } = useTranslation();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const deleteMutation = useDeleteUserDataFineTune();
  const { success, error } = useSmartNotification();

  // Handle delete confirmation
  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteMutation.mutateAsync(dataset.id);
      setShowDeleteModal(false);

      // Show success notification
      success({
        title: t('user-dataset:datasetCard.deleteSuccess.title', 'Thành công'),
        message: t(
          'user-dataset:datasetCard.deleteSuccess.message',
          'Dataset đã được xóa thành công!'
        ),
        duration: 5000,
      });

      // Call parent onDelete callback if provided
      if (onDelete) {
        onDelete();
      }
    } catch (err) {
      console.error('Failed to delete dataset:', err);

      // Show error notification
      error({
        title: t('user-dataset:datasetCard.deleteError.title', 'Lỗi'),
        message: t(
          'user-dataset:datasetCard.deleteError.message',
          'Không thể xóa dataset. Vui lòng thử lại.'
        ),
        duration: 5000,
      });

      // Keep modal open on error
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Status chip variants
  const getStatusVariant = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'danger';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('user-dataset:datasetStatus.pending', 'Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('user-dataset:datasetStatus.processing', 'Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('user-dataset:datasetStatus.completed', 'Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('user-dataset:datasetStatus.failed', 'Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('user-dataset:datasetStatus.cancelled', 'Đã hủy');
      default:
        return status;
    }
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 ${className}`}
      variant="elevated"
      onClick={onClick}
    >
      <div className="p-3">
        {/* Header: Icon + Tên + Status */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {/* Icon nhỏ */}
            <div className="w-8 h-8 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-sm">🛠️</span>
            </div>

            {/* Tên dataset */}
            <h3 className="font-medium text-sm text-gray-900 dark:text-white truncate">
              {dataset.name}
            </h3>
          </div>

          {/* Status chip nhỏ */}
          <Chip
            variant={getStatusVariant(dataset.status)}
            size="sm"
            className="text-xs font-medium flex-shrink-0"
          >
            {getStatusLabel(dataset.status)}
          </Chip>
        </div>

        {/* Mô tả ngắn */}
        <div className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2 leading-relaxed">
          {dataset.description || t('noDescription', 'Không có mô tả')}
        </div>

        {/* Footer: Actions */}
        <div className="flex items-center justify-end">
          {/* Action buttons nhỏ */}
          <div className="flex items-center space-x-1">
            <Tooltip content={t('delete', 'Xóa')} position="top">
              <IconCard
                icon="trash"
                variant="ghost"
                size="sm"
                onClick={handleDeleteClick}
                disabled={dataset.status === DataFineTuneStatus.PROCESSING}
              />
            </Tooltip>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        itemName={dataset.name}
        isSubmitting={deleteMutation.isPending}
        title={t(' user-dataset:datasetCard.deleteConfirm.title', 'Xác nhận xóa dataset')}
        message={t(
          'user-dataset:datasetCard.deleteConfirm.message',
          'Bạn có chắc chắn muốn xóa dataset này? Hành động này không thể hoàn tác.'
        )}
      />
    </Card>
  );
};

export default DatasetCard;
