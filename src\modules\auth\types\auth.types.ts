/**
 * Trạng thái người dùng
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

/**
 * Thông tin người dùng
 */
export interface User {
  id: string;
  email: string;
  fullName: string;
  avatar?: string;
  role: string;
  status: UserStatus;
  permissions?: string[];
}

/**
 * Thông tin xác thực tài khoản
 */
export interface VerifyAccountInfo {
  platform: string;
  value: string;
}

/**
 * Yêu cầu đăng nhập
 */
export interface LoginRequest {
  email: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng nhập
 */
export interface LoginResponse {
  accessToken?: string;
  expiresIn: number;
  expiresAt?: number; // Thời điểm hết hạn (timestamp)
  user?: User;
  info?: VerifyAccountInfo[];
  verifyToken?: string;
}

/**
 * Yêu cầu đăng ký
 */
export interface RegisterRequest {
  fullName: string;
  email: string;
  password: string;
  phoneNumber: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng ký
 */
export interface RegisterResponse {
  otpToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
  maskedEmail?: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
  info?: VerifyAccountInfo[]; // Thông tin xác thực
}

/**
 * Yêu cầu xác thực OTP
 */
export interface VerifyOtpRequest {
  otpToken: string;
  otp: string;
  platform?: string; // Làm cho trường này trở thành optional
}

/**
 * Phản hồi xác thực OTP
 */
export interface VerifyOtpResponse {
  accessToken: string;
  expiresIn: number;
  expiresAt?: number; // Thời điểm hết hạn (timestamp)
  user: User;
}

/**
 * Yêu cầu gửi lại OTP
 */
export interface ResendOtpRequest {
  otpToken: string;
  platform?: string; // Làm cho trường này trở thành optional vì backend không cần
}

/**
 * Phản hồi gửi lại OTP
 */
export interface ResendOtpResponse {
  otpToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
  maskedEmail?: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
}

/**
 * Yêu cầu quên mật khẩu
 */
export interface ForgotPasswordRequest {
  email: string;
}

/**
 * Phản hồi quên mật khẩu
 */
export interface ForgotPasswordResponse {
  otpToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
  maskedEmail: string; // Email đã được che một phần
  otp?: string; // Mã OTP (chỉ có trong môi trường development)
}

/**
 * Yêu cầu xác thực quên mật khẩu
 */
export interface VerifyForgotPasswordRequest {
  otpToken: string;
  otp: string;
}

/**
 * Phản hồi xác thực quên mật khẩu
 */
export interface VerifyForgotPasswordResponse {
  changePasswordToken: string;
  expiresAt: number; // Thời điểm hết hạn (timestamp)
}

/**
 * Yêu cầu đặt lại mật khẩu
 */
export interface ResetPasswordRequest {
  newPassword: string;
  changePasswordToken: string;
}

/**
 * Phản hồi đặt lại mật khẩu
 */
export interface ResetPasswordResponse {
  success: boolean;
}

/**
 * Yêu cầu làm mới token
 */
export interface RefreshTokenRequest {
  refreshToken?: string;
}

/**
 * Phản hồi làm mới token
 */
export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

/**
 * Yêu cầu đăng nhập Google
 */
export interface GoogleAuthRequest {
  code: string;
  redirectUri?: string;
}

/**
 * Yêu cầu đăng nhập Facebook
 */
export interface FacebookAuthRequest {
  code: string;
  redirectUri?: string;
}

/**
 * Yêu cầu đăng nhập Zalo
 */
export interface ZaloAuthRequest {
  code: string;
  redirectUri?: string;
}

/**
 * Auth state interface
 */
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  expiresIn: number | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Saved credentials interface
 */
export interface SavedCredentials {
  username: string;
  encryptedPassword: string; // Mật khẩu đã được mã hóa
  timestamp: number;
}

/**
 * Admin login request
 */
export interface AdminLoginRequest extends LoginRequest {
  role: string;
}

/**
 * Admin forgot password request
 */
export interface AdminForgotPasswordRequest extends ForgotPasswordRequest {
  role: string;
}

/**
 * Admin verify forgot password request
 */
export interface AdminVerifyForgotPasswordRequest {
  token: string;
  code: string;
  role: string;
}

/**
 * Admin reset password request
 */
export interface AdminResetPasswordRequest {
  token: string;
  password: string;
  role: string;
}

/**
 * Admin resend OTP request
 */
export interface AdminResendOtpRequest {
  email: string;
  role: string;
}

/**
 * Yêu cầu xác thực hai lớp
 */
export interface VerifyTwoFactorRequest {
  verifyToken: string;
  method: string;
  code: string;
}

/**
 * Phản hồi xác thực hai lớp
 */
export interface VerifyTwoFactorResponse {
  accessToken: string;
  expiresIn: number;
  expiresAt?: number; // Thời điểm hết hạn (timestamp)
  user: User;
}

/**
 * Yêu cầu chọn phương thức xác thực hai lớp
 */
export interface SelectTwoFactorMethodRequest {
  token: string;
  platform: 'EMAIL' | 'SMS';
}

/**
 * Phản hồi chọn phương thức xác thực hai lớp
 */
export interface SelectTwoFactorMethodResponse {
  message: string;
  expiresAt?: number;
}
