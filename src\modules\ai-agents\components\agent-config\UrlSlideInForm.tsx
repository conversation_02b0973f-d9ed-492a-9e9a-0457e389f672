import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  FormItem,
  Icon,
  IconCard,
  Input,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useUrls, useCreateUrl } from '@/modules/data/url/hooks/useUrlQuery';
import { FindAllUrlDto, CreateUrlDto } from '@/modules/data/url/types/url.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { Url } from '@/modules/ai-agents/types/response';
import { useAgentUrls, useAddUrls } from '@/modules/ai-agents/hooks/useAgentResources';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component UrlSlideInForm
 */
interface UrlSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * Callback khi có tài nguyên được thêm (chỉ dùng cho mode create)
   */
  onResourceAdded?: (items: Url[]) => void;

  /**
   * Danh sách items đã chọn ban đầu (chỉ dùng cho mode create)
   */
  initialSelectedItems?: Url[];
}

/**
 * Component form trượt để chọn các URL
 */
const UrlSlideInForm: React.FC<UrlSlideInFormProps> = ({
  isVisible,
  onClose,
  agentId,
  mode,
  onResourceAdded,
  initialSelectedItems = [],
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    updateSuccess,
    updateError,
    validationError,
    createSuccess,
    createError,
    success
  } = useAiAgentNotification();

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  // const [hasChanges, setHasChanges] = useState<boolean>(false);

  // API hooks
  const { data: agentUrlsResponse } = useAgentUrls(agentId && mode === 'edit' ? agentId : '');
  const addUrlsMutation = useAddUrls();

  // Lấy danh sách URLs đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedUrlIds = React.useMemo(() => {
    return agentUrlsResponse?.items?.map(item => item.id) || [];
  }, [agentUrlsResponse?.items]);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<FindAllUrlDto>({
    page: 1,
    limit: 10,
    keyword: '',
    sortBy: 'createdAt',
    sortDirection: SortDirection.DESC,
  });

  // Khởi tạo selectedIds từ agent URLs hoặc initialSelectedItems
  useEffect(() => {
    if (mode === 'edit' && selectedUrlIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedUrlIds);
    } else if (mode === 'create' && initialSelectedItems.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedItems.map(item => String(item.id)));
    }
  }, [selectedUrlIds, mode, selectedIds.length, initialSelectedItems]);

  // State cho thêm URL mới
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newUrlTitle, setNewUrlTitle] = useState<string>('');
  const [newUrlAddress, setNewUrlAddress] = useState<string>('');
  const [newUrlDescription, setNewUrlDescription] = useState<string>('');

  // API hooks
  const { data: urlResponse, isLoading } = useUrls(queryParams);
  const createUrlMutation = useCreateUrl();

  // Lấy dữ liệu từ API response và chuyển đổi sang format Url
  const urls: Url[] = (urlResponse?.items || []).map(item => ({
    id: item.id,
    title: item.title,
    url: item.url,
    description: item.content,
    category: item.type || '',
    createdAt: new Date(item.createdAt * 1000).toISOString().split('T')[0] || '',
    status: 'active' as const, // Mặc định là active vì API không có field status
  }));

  const totalItems = urlResponse?.meta?.totalItems || 0;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Url>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'url',
      title: t('aiAgents:urlSlideInForm.url'),
      dataIndex: 'title',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-md bg-blue-100 flex items-center justify-center mr-3">
            <Icon name="link" size="md" className="text-blue-600" />
          </div>
          <div>
            <Typography variant="subtitle1">{record.title}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.url}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: t('aiAgents:urlSlideInForm.category'),
      dataIndex: 'category',
      width: '20%',
    },
    {
      key: 'status',
      title: t('aiAgents:urlSlideInForm.status'),
      dataIndex: 'status',
      width: '20%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              {t('aiAgents:urlSlideInForm.active')}
            </span>
          ) : record.status === 'pending' ? (
            <span className="text-yellow-500 text-sm flex items-center">
              <Icon name="clock" size="sm" className="mr-1" />
              {t('aiAgents:urlSlideInForm.pending')}
            </span>
          ) : record.status === 'broken' ? (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              {t('aiAgents:urlSlideInForm.broken')}
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              {t('aiAgents:urlSlideInForm.pending')}
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'visit',
      title: t('common:view'),
      width: '20%',
      render: (_, record) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            window.open(record.url, '_blank');
          }}
        >
           <Icon name="eye" size="sm" className="" />
        </Button>
      ),
    },
  ];

  // Kiểm tra có thay đổi chưa lưu không
  // useEffect(() => {
  //   const hasUnsavedChanges = mode === 'edit' && (
  //     selectedIds.length !== selectedUrlIds.length ||
  //     selectedIds.some(id => !selectedUrlIds.includes(id)) ||
  //     selectedUrlIds.some(id => !selectedIds.includes(id))
  //   );

  //   setHasChanges(hasUnsavedChanges || selectedIds.length > 0);
  // }, [selectedIds, selectedUrlIds, mode]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams(prev => ({
      ...prev,
      keyword: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: SortDirection) => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: column,
      sortDirection: direction,
    }));
  };

  // Xử lý thêm URL mới
  const handleAddUrl = async () => {
    if (!newUrlTitle || !newUrlAddress) {
      validationError('Vui lòng nhập đầy đủ thông tin URL');
      return;
    }

    // Kiểm tra URL hợp lệ
    try {
      new URL(newUrlAddress);
    } catch {
      validationError('URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)');
      return;
    }

    try {
      // Tạo URL mới qua API
      const newUrlData: CreateUrlDto = {
        title: newUrlTitle,
        url: newUrlAddress,
        content: newUrlDescription,
        type: 'Khác',
      };

      const result = await createUrlMutation.mutateAsync(newUrlData);

      // Thêm vào danh sách đã chọn
      setSelectedIds(prev => [...prev, String(result.result.id)]);

      // Reset form
      setNewUrlTitle('');
      setNewUrlAddress('');
      setNewUrlDescription('');
      setShowAddForm(false);

      // Reload dữ liệu
      setQueryParams(prev => ({ ...prev, page: 1 }));
      createSuccess(t('aiAgents:responseConfig.url'));
    } catch (error) {
      console.error('Error creating URL:', error);
      createError(t('aiAgents:responseConfig.url'), error instanceof Error ? error.message : undefined);
    }
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentId) {
        validationError(t('common:error'));
        return;
      }

      setIsSubmitting(true);
      try {
        // Gọi API để cập nhật URLs cho agent
        await addUrlsMutation.mutateAsync({
          agentId,
          data: { urlIds: selectedIds }
        });

        updateSuccess(t('aiAgents:responseConfig.url'));

        onClose();
      } catch (error) {
        updateError(t('aiAgents:responseConfig.url'), error instanceof Error ? error.message : undefined);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách đã chọn
      const selectedUrlItems = urls.filter(item => selectedIds.includes(item.id));

      if (onResourceAdded) {
        onResourceAdded(selectedUrlItems);
      }

      success({
        message: t('aiAgents:urlSlideInForm.addUrlsToListSuccess'),
      });

      onClose();
    }
  };

  // Xử lý đóng form - không cần confirm
  const handleClose = useCallback(() => {
    setQueryParams(prev => ({ ...prev, keyword: '' }));
    setShowAddForm(false);
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: t('aiAgents:urlSlideInForm.sortBy'),
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-title',
      label: 'Tiêu đề',
      onClick: () => handleSortChange('title', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: t('aiAgents:urlSlideInForm.all'),
      onClick: () => setQueryParams(prev => ({ ...prev, keyword: '' })),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:urlSlideInForm.title')}</Typography>
            {/* Nút lưu */}
        <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('aiAgents:urlSlideInForm.cancel')}
            onClick={handleClose}
            disabled={isSubmitting}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('aiAgents:urlSlideInForm.save')}
            onClick={handleSave}
            disabled={isLoading || isSubmitting || (mode === 'edit' && !agentId) || selectedIds.length === 0}
            isLoading={isSubmitting}
          />
         
        </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => setShowAddForm(!showAddForm)}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Form thêm URL mới */}
        {showAddForm && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle1" className="mb-3">{t('aiAgents:resourcesConfig.addUrl')}</Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <FormItem label={t('aiAgents:urlSlideInForm.title')}>
                  <Input
                    value={newUrlTitle}
                    onChange={(e) => setNewUrlTitle(e.target.value)}
                    placeholder={t('aiAgents:urlSlideInForm.title')}
                    fullWidth
                  />
                </FormItem>
              </div>
              <div>
                <FormItem label={t('aiAgents:urlSlideInForm.url')}>
                  <Input
                    value={newUrlAddress}
                    onChange={(e) => setNewUrlAddress(e.target.value)}
                    placeholder="https://example.com"
                    fullWidth
                  />
                </FormItem>
              </div>
            </div>
            <div className="mb-3">
              <FormItem label={t('aiAgents:urlSlideInForm.description')}>
                <Input
                  value={newUrlDescription}
                  onChange={(e) => setNewUrlDescription(e.target.value)}
                  placeholder={t('aiAgents:urlSlideInForm.description')}
                  fullWidth
                />
              </FormItem>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="mr-2"
              >
                {t('aiAgents:urlSlideInForm.cancel')}
              </Button>
              <Button
                variant="primary"
                onClick={handleAddUrl}
                disabled={!newUrlTitle || !newUrlAddress}
              >
                {t('common:add')}
              </Button>
            </div>
          </div>
        )}

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<Url>
            columns={columns}
            data={urls}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== (queryParams.limit || 10)) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </div>

      
      </Card>
    </SlideInForm>
  );
};

export default UrlSlideInForm;
