import React from 'react';
import { Button } from '@/shared/components/common';
import { CustomAgentFormData } from './CustomTypeAgentCard';

interface CreateAgentFormProps {
  isCustomAgentSelected: boolean;
  customAgentData: CustomAgentFormData;
  selectedAgentName?: string;
  selectedAgentDescription?: string;
  onContinue?: () => void;
}

/**
 * Component hiển thị thông tin agent đã chọn hoặc đã tạo
 */
const CreateAgentForm: React.FC<CreateAgentFormProps> = ({
  isCustomAgentSelected,
  customAgentData,
  selectedAgentName,
  selectedAgentDescription,
  onContinue
}) => {
  // Kiểm tra xem có hiển thị thông tin custom agent không
  const showCustomAgentInfo = !isCustomAgentSelected && customAgentData.name !== "Custom Agent";

  // Kiểm tra xem có hiển thị thông tin agent đ<PERSON><PERSON><PERSON> chọn không
  const showSelectedAgentInfo = !isCustomAgentSelected && selectedAgentName;

  if (!showCustomAgentInfo && !showSelectedAgentInfo) {
    return null;
  }

  return (
    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      {showSelectedAgentInfo && (
        <>
          <h3 className="font-medium text-lg mb-2">Đã chọn: {selectedAgentName}</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{selectedAgentDescription}</p>
          <Button variant="primary" onClick={onContinue}>Tiếp tục với {selectedAgentName}</Button>
        </>
      )}

      {showCustomAgentInfo && (
        <>
          <h3 className="font-medium text-lg mb-2">Đã tạo Custom Agent: {customAgentData.name}</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{customAgentData.description}</p>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${customAgentData.config.enableAgentProfileCustomization ? 'bg-green-500' : 'bg-gray-300'}`}></span>
              <span>Hỗ trợ Profile: {customAgentData.config.enableAgentProfileCustomization ? 'Có' : 'Không'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${customAgentData.config.enableOutputToMessenger ? 'bg-green-500' : 'bg-gray-300'}`}></span>
              <span>Hỗ trợ Output: {customAgentData.config.enableOutputToMessenger ? 'Có' : 'Không'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${customAgentData.config.enableTaskConversionTracking ? 'bg-green-500' : 'bg-gray-300'}`}></span>
              <span>Hỗ trợ Conversion: {customAgentData.config.enableTaskConversionTracking ? 'Có' : 'Không'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${customAgentData.config.enableResourceUsage ? 'bg-green-500' : 'bg-gray-300'}`}></span>
              <span>Hỗ trợ Resources: {customAgentData.config.enableResourceUsage ? 'Có' : 'Không'}</span>
            </div>
          </div>
          <Button variant="primary" onClick={onContinue}>Tiếp tục với {customAgentData.name}</Button>
        </>
      )}
    </div>
  );
};

export default CreateAgentForm;
