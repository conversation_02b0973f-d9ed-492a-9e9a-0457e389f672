{"admin-dataset": {"title": "Q<PERSON><PERSON>n lý Dataset & Model", "description": "<PERSON><PERSON><PERSON><PERSON> lý dataset và model AI cho người dùng", "dataFineTune": {"title": "Dataset Fine-tune", "description": "<PERSON><PERSON><PERSON><PERSON> lý dataset để huấn luyện và fine-tune các model AI."}, "createDataset": {"notification": {"success": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "message": "Dataset OpenAI đã đư<PERSON>c tạo thành công!", "messageGoogle": "Dataset Google đã được tạo thành công!"}, "error": {"title": "Lỗi", "message": "<PERSON><PERSON><PERSON>ng thể tạo dataset. <PERSON><PERSON> lòng thử lại."}}}, "menu": {"overview": "<PERSON><PERSON><PERSON> quan", "dataFineTune": "Dataset Fine-tune", "createOpenAI": "Tạo Dataset OpenAI", "createGoogle": "Tạo Dataset Google"}, "createFineTuneModel": {"title": "<PERSON><PERSON><PERSON> Fine-tune Model", "form": {"modelName": "Tên model", "description": "<PERSON><PERSON>", "dataset": "Dataset", "baseModel": "Base Model", "suffix": "Suffix", "modelSource": "Nguồn Base Model", "userKeyLLM": "Chọn User Key LLM", "hyperparameters": "<PERSON><PERSON><PERSON> h<PERSON>nh Hyperparameters", "epochs": "Epochs", "batchSize": "<PERSON><PERSON> Si<PERSON>", "learningRate": "Learning Rate"}, "placeholders": {"modelName": "<PERSON><PERSON><PERSON><PERSON> tên model", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả model", "suffix": "Nhập suffix cho model", "selectDataset": "-- Chọn dataset để fine-tune --", "selectSystemModel": "-- Chọn system model --", "selectUserModel": "-- Chọn user model --", "selectUserKey": "-- <PERSON><PERSON><PERSON>r Key --", "selectKeyFirst": "<PERSON><PERSON> lòng chọn key LLM trước"}, "buttons": {"systemModels": "System Models", "userModels": "User Models (Keys)", "auto": "Auto", "custom": "Custom", "cancel": "<PERSON><PERSON><PERSON>", "create": "Tạo Model"}, "loading": {"datasets": "Đang tải datasets...", "systemModels": "Đang tải system models...", "userKeys": "<PERSON><PERSON> tải danh s<PERSON>ch keys...", "userModels": "<PERSON><PERSON> t<PERSON> models từ key...", "userKeysLoading": "<PERSON><PERSON> tải danh sách User Keys..."}, "messages": {"noDatasets": "Không có dataset nào", "noUserKeys": "K<PERSON>ông có User Key nào", "noUserKeysAvailable": "<PERSON><PERSON><PERSON><PERSON> có User Key LLM nào khả dụng", "noModelsAvailable": "<PERSON><PERSON><PERSON>ng có model n<PERSON><PERSON> kh<PERSON> dụng", "noModelsForKey": "Không có models nào khả dụng cho key này", "selectKeyFirst": "Chọn User Key LLM để xem danh sách models có sẵn", "loadingModelsFromKey": "<PERSON><PERSON> tải models từ key đã chọn...", "modelsFound": "T<PERSON><PERSON> thấy {{count}} models từ key này", "loadingError": "Lỗi khi tải models: {{error}}"}}, "apiIntegration": {"title": "T<PERSON>ch hợp <PERSON>", "tabs": {"systemModels": "<PERSON><PERSON> h<PERSON>nh hệ thống", "userModels": "<PERSON><PERSON> hình ng<PERSON>ờ<PERSON> dùng", "fineTuneModels": "<PERSON>h chỉnh các mô hình"}, "loading": {"loadingData": "<PERSON><PERSON> tải dữ liệu..."}, "messages": {"noUserKeys": "K<PERSON>ông có User Key nào", "noModelsForKey": "<PERSON><PERSON><PERSON><PERSON> có <PERSON> nào cho Key đã chọn", "noSystemModels": "Không có System Model nào"}, "form": {"selectUserKey": "<PERSON>ọn User Key để xem Models"}, "placeholders": {"selectUserKey": "-- <PERSON><PERSON><PERSON>r Key --"}}, "providerSelection": {"createDatasetUsingOpenAI": "Tạo Dataset sử dụng OpenAI", "createDatasetUsingGoogle": "Tạo Dataset sử dụng Google"}, "chatLayout": {"training": {"title": "Chat huấn luyện dataset", "description": "Import file JSONL hoặc tạo conversation mới cho training data", "defaultConversationTitle": "Training Conversation", "defaultChatTitle": "Training Chat", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn cho Training Data...", "messages": {"importSuccess": "✅ Đã import thành công {{count}} conversation(s) từ file {{type}}", "importError": "⚠️ Không có conversation hợp lệ nào được import. <PERSON><PERSON> lòng kiểm tra định dạng file.", "parseError": "Lỗi phân tích file: {{error}}. <PERSON>ui lòng kiểm tra định dạng file."}}, "validation": {"title": "<PERSON>t ki<PERSON>m đ<PERSON>nh dataset", "description": "Import file JSONL hoặc tạo conversation mới cho validation data", "defaultConversationTitle": "Validation Conversation", "defaultChatTitle": "Validation Chat", "placeholder": "Nhậ<PERSON> tin nhắn cho Validation Data..."}}, "conversationSidebar": {"title": "<PERSON><PERSON><PERSON> tho<PERSON>i", "buttons": {"closeSidebar": "Đóng sidebar", "openSidebar": "Mở sidebar", "importJsonl": "Import JSONL", "newChat": "Đoạn chat mới", "deleteConversation": "Xóa conversation"}, "messages": {"noConversations": "Chưa có <PERSON> nào", "importToStart": "Import file JSONL để bắt đầu"}}, "chatPanelWithRoleLogic": {"startConversation": "<PERSON><PERSON><PERSON> đầu conversation", "startConversationDescription": "Thê<PERSON> tin nhắn để tạo dataset huấn luyện"}, "dataFineTunePage": {"status": {"pending": "<PERSON><PERSON> chờ", "processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "cancelled": "<PERSON><PERSON> hủy"}, "filter": {"allStatus": "<PERSON><PERSON><PERSON> cả trạng thái"}, "messages": {"noDatasets": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dataset nào"}}, "datasetCard": {"deleteSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "message": "Dataset đã đư<PERSON>c xóa thành công!"}, "deleteError": {"title": "Lỗi", "message": "<PERSON><PERSON><PERSON>ng thể xóa dataset. <PERSON><PERSON> lòng thử lại."}, "deleteConfirm": {"title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "message": "Bạn có chắc chắn muốn xóa dataset này không? Hành động này không thể hoàn tác."}, "statusUpdateSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "message": "Trạng thái dataset đã đ<PERSON><PERSON><PERSON> cập nhật thành công!"}, "statusUpdateError": {"title": "Lỗi", "message": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái dataset. <PERSON><PERSON> lòng thử lại."}}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON>c", "description": "<PERSON><PERSON><PERSON><PERSON> lý các dataset đã xóa mềm, bao gồm khôi phục và xóa vĩnh viễn dataset."}, "datasetStatus": {"pending": "<PERSON><PERSON> chờ", "processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "cancelled": "<PERSON><PERSON> hủy"}}}