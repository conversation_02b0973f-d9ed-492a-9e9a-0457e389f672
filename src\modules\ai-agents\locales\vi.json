{"aiAgents": {"notification": {"createSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "deleteSuccess": "{{entityName}} đ<PERSON> đư<PERSON>c xóa thành công", "copySuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON>c sao chép thành công", "shareSuccess": "{{entityName}} đã được chia sẻ thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo {{entityName}}", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật {{entityName}}", "deleteError": "Có lỗi xảy ra khi xóa {{entityName}}", "copyError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi sao chép {{entityName}}", "shareError": "<PERSON><PERSON> lỗi xảy ra khi chia sẻ {{entityName}}", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách {{entityName}}", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "uploadSuccessWithName": "<PERSON><PERSON><PERSON> lên {{fileName}} thành công", "uploadError": "<PERSON><PERSON> lỗi xảy ra khi tải lên", "validationError": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "permissionError": "<PERSON><PERSON>n không có quyền thực hiện thao tác này", "networkError": "Lỗi kết nối mạng. <PERSON><PERSON> lòng thử lại", "processing": "<PERSON><PERSON> {{action}}...", "saveSuccess": "<PERSON><PERSON> lưu thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu", "publishSuccess": "{{entityName}} đ<PERSON> đư<PERSON>c xuất bản thành công", "publishError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi xuất bản {{entityName}}", "restoreSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> khôi phục thành công", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục {{entityName}}"}, "common": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "update": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới", "select": "<PERSON><PERSON><PERSON>", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Đ<PERSON> xảy ra lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "confirm": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa?", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "close": "Đ<PERSON><PERSON>", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó", "finish": "<PERSON><PERSON><PERSON> th<PERSON>", "agentDetail": "<PERSON> tiết Agent"}, "aiAgents": "AI Agent", "agentCreate": {"title": "Tạo Agent", "customAgentButton": "Tạo Agent tùy chỉnh", "selectAgentType": "<PERSON><PERSON><PERSON>", "selectAgentDescription": "<PERSON><PERSON><PERSON> một loại Agent từ danh sách dưới đây hoặc tạo Agent tùy chỉnh của riêng bạn.", "configureAgent": "<PERSON><PERSON><PERSON> h<PERSON>nh {name}", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "sortName": "<PERSON><PERSON><PERSON>", "sortDate": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON> tự", "orderAsc": "<PERSON><PERSON><PERSON>", "orderDesc": "<PERSON><PERSON><PERSON><PERSON>", "agentTypeDescription": "Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có những khả năng và đặc điểm khác nhau."}, "integrationConfig": {"title": "<PERSON><PERSON><PERSON>", "facebook": "Facebook", "website": "Website", "addFacebook": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "addWebsite": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON> Website", "noFacebookIntegration": "Chưa có tích hợp Facebook nào", "noWebsiteIntegration": "Chưa có tích hợp Website nào", "selectFacebook": "<PERSON><PERSON><PERSON> t<PERSON>", "selectWebsite": "Chọn Website", "facebookPageName": "<PERSON><PERSON><PERSON> t<PERSON>", "websiteName": "Tên Website", "websiteUrl": "URL Website"}, "strategyConfig": {"title": "<PERSON><PERSON><PERSON>", "selectStrategy": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> cho <PERSON>", "selectStrategyDescription": "<PERSON><PERSON><PERSON> một chiến lư<PERSON><PERSON> và cấu hình các bước xử lý", "basicStrategy": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> c<PERSON> bản", "basicStrategyDescription": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> đơn giản với các cài đặt mặc định", "advancedStrategy": "<PERSON><PERSON><PERSON> nâng cao", "advancedStrategyDescription": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> với các tùy chọn nâng cao và xử lý phức tạp", "customStrategy": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> tùy chỉnh", "customStrategyDescription": "<PERSON><PERSON><PERSON> chiến lược riêng với các cài đặt tùy chỉnh hoàn toàn", "configureStrategy": "<PERSON><PERSON><PERSON> hình ch<PERSON>", "step": "B<PERSON><PERSON><PERSON> {number}", "input": "Input"}, "convertConfig": {"title": "<PERSON><PERSON><PERSON> hình chuyển đổi", "configureFields": "<PERSON><PERSON><PERSON> hình các trường dữ liệu cần thu thập", "configureFieldsDescription": "<PERSON><PERSON><PERSON> các trường dữ liệu mà Agent sẽ thu thập từ người dùng", "noFields": "<PERSON><PERSON><PERSON> có trường dữ liệu nào đ<PERSON><PERSON><PERSON> cấu hình", "addField": "<PERSON>h<PERSON><PERSON> trư<PERSON><PERSON> mới", "editField": "Chỉnh sửa trường", "fieldName": "<PERSON><PERSON><PERSON> tr<PERSON>", "fieldDescription": "<PERSON><PERSON>", "fieldType": "<PERSON><PERSON><PERSON> dữ liệu", "fieldRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "fieldNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tr<PERSON> (vd: email)", "fieldDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả của trường (vd: <PERSON><PERSON><PERSON> tất cả email của người dùng)", "pleaseEnterAllFields": "<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin trường", "text": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "number": "Số", "date": "<PERSON><PERSON><PERSON>", "address": "Địa chỉ", "name": "<PERSON><PERSON> tên"}, "responseConfig": {"title": "<PERSON><PERSON><PERSON> nguyên ph<PERSON>n hồi", "configureResources": "<PERSON><PERSON><PERSON> hình tài nguyên phản hồi cho <PERSON>", "configureResourcesDescription": "<PERSON><PERSON><PERSON> các tài nguyên mà Agent có thể sử dụng để phản hồi người dùng", "media": "T<PERSON>i <PERSON>n <PERSON>", "url": "<PERSON><PERSON><PERSON> ng<PERSON>n URL", "product": "<PERSON><PERSON><PERSON> ng<PERSON>m", "noMedia": "Chưa có tài nguyên Media nào", "noUrl": "Chưa có tài nguyên URL nào", "noProduct": "<PERSON><PERSON><PERSON> có tài nguyên <PERSON>ản phẩm nào", "selectMedia": "Chọn media", "selectUrl": "Chọn URL", "selectProduct": "<PERSON><PERSON><PERSON> sản ph<PERSON>m"}, "editAgent": {"title": "Chỉnh sửa Agent", "subtitle": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin và cấu hình của Agent", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "agentName": "<PERSON><PERSON>n <PERSON>", "agentNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tê<PERSON>", "agentDescription": "<PERSON><PERSON> t<PERSON>", "agentDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho Agent", "agentAvatar": "Ảnh đại diện", "changeAvatar": "<PERSON>hay đổi <PERSON>nh đại di<PERSON>n", "agentStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "agentLevel": "<PERSON><PERSON><PERSON>", "agentExp": "<PERSON><PERSON>", "agentModel": "Model AI", "agentType": "Loại Agent", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "cancelEdit": "<PERSON><PERSON>y chỉnh sửa", "deleteAgent": "Xóa Agent", "confirmDelete": "Bạn có chắc chắn muốn xóa Agent n<PERSON><PERSON>?", "deleteWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t Agent thành công!", "updateError": "<PERSON><PERSON><PERSON> nhật Agent thất bại!", "deleteSuccess": "Xóa Agent thành công!", "deleteError": "Xóa Agent thất bại!", "validation": {"nameRequired": "Tên Agent <PERSON><PERSON><PERSON><PERSON>", "nameMinLength": "Tên Agent ph<PERSON><PERSON> có ít nhất 2 ký tự", "nameMaxLength": "Tên Agent kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự"}}, "mediaSlideInForm": {"title": "Chọn media", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "media": "Media", "type": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON>", "view": "Xem", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "name": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu trong chế độ này.", "updateMediaSuccess": "<PERSON><PERSON>p nhật media thành công!", "updateMediaError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật media.", "addMediaToListSuccess": "Đã thêm media vào danh sách!", "loadingFacebookPages": "<PERSON><PERSON> tải danh s<PERSON>ch Facebook Pages...", "errorLoadingFacebookPages": "Lỗi khi tải danh s<PERSON>ch Facebook Pages", "noFacebookIntegrations": "Chưa có tích hợp Facebook nào", "loadingWebsites": "<PERSON><PERSON> tải danh sách Websites...", "errorLoadingWebsites": "Lỗi khi tải danh sách Websites", "noWebsiteIntegrations": "Chưa có tích hợp Website nào"}, "facebookSlideInForm": {"title": "<PERSON><PERSON><PERSON> t<PERSON>", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "facebookPage": "Trang Facebook", "category": "<PERSON><PERSON>", "followers": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "name": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu trong chế độ này.", "integratedSuccessfully": "<PERSON><PERSON><PERSON> hợp thành công {{count}} Facebook Page!", "cannotIntegrate": "{{count}} Facebook Page không thể tích hợp: {{details}}", "noPageIntegratedSuccessfully": "Không có Facebook Page nào đư<PERSON><PERSON> tích hợp thành công!", "updateIntegrationError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật tích hợp Facebook.", "selectedFacebookIntegration": "<PERSON><PERSON> chọn tích hợp Facebook!", "connectedPages": "<PERSON><PERSON> kết nối", "notConnectedPages": "<PERSON><PERSON><PERSON> k<PERSON>"}, "websiteSlideInForm": {"title": "Chọn website", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "website": "Website", "category": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "connected": "<PERSON><PERSON> kết nối", "processing": "<PERSON><PERSON> lý", "connectionError": "Lỗi kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "host": "Host", "createdDate": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "verified": "Đ<PERSON> x<PERSON>c <PERSON>h", "notVerified": "<PERSON><PERSON><PERSON> x<PERSON>h", "addNewWebsite": "Thêm website mới", "websiteName": "Tên website", "url": "URL", "enterWebsiteName": "Nhập tên website", "enterWebsiteUrl": "https://example.com", "add": "<PERSON><PERSON><PERSON><PERSON>", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu trong chế độ này.", "integratedSuccessfully": "<PERSON><PERSON><PERSON> hợp thành công {{count}} Website!", "cannotIntegrate": "{{count}} Website không thể tích hợp: {{details}}", "noWebsiteIntegratedSuccessfully": "Không có Website nào đư<PERSON><PERSON> tích hợp thành công!", "updateIntegrationError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật tích hợp Website.", "selectedWebsiteIntegration": "<PERSON><PERSON> chọn tích hợp Website!", "pleaseEnterAllInfo": "<PERSON><PERSON> lòng nhập đầy đủ thông tin website", "invalidUrl": "URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)", "addWebsiteSuccess": "Thêm website thành công!", "addWebsiteError": "Có lỗi xảy ra khi thêm website"}, "integration": {"title": "<PERSON><PERSON><PERSON>", "connectAgentToPlatforms": "<PERSON>ết nối Agent v<PERSON><PERSON> c<PERSON>c n<PERSON>n tảng", "connectDescription": "Kết nối Agent vớ<PERSON> các trang Facebook và Website để tương tác với người dùng", "connectAgentToWebsite": "<PERSON>ết nối Agent với Website", "websiteDescription": "Kết nối Agent vớ<PERSON> các Website để tương tác với người dùng", "connectAgentToFacebook": "Kết nối Agent với Facebook", "facebookDescription": "<PERSON>ết nối Agent vớ<PERSON> các trang Facebook để tương tác với người dùng", "facebookIntegration": "<PERSON><PERSON><PERSON>", "websiteIntegration": "<PERSON><PERSON><PERSON> Website", "facebookPages": "Trang Facebook", "websites": "Website", "add": "Thêm Website", "confirmDeleteIntegration": "<PERSON>ạn có chắc chắn muốn xóa tích hợp \"{{integrationName}}\" khỏi Agent không?", "deleteIntegrationWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "removeFacebookSuccess": "<PERSON><PERSON><PERSON> tích hợp Facebook thành công!", "removeFacebookError": "Có lỗi xảy ra khi xóa tích hợp Facebook.", "removeWebsiteSuccess": "<PERSON><PERSON><PERSON> tích hợp Website thành công!", "removeWebsiteError": "Có lỗi xảy ra khi xóa tích hợp Website."}, "multiAgent": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh Multi-Agent", "description": "<PERSON><PERSON>u hình nhiều agent đ<PERSON> làm việc cùng nhau. Mỗi agent sẽ có vai trò và chức năng riêng biệt.", "addAgent": "<PERSON>hêm Agent", "agentList": "<PERSON><PERSON> ", "noAgents": "Chưa có agent nào", "addFirstAgent": "Thêm agent đ<PERSON><PERSON> tiên để bắt đầu cấu hình multi-agent", "moveUp": "<PERSON> chuyển lên", "moveDown": "<PERSON> xuống", "editDescription": "Chỉnh sửa mô tả", "deleteAgent": "Xóa agent", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "enterDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả cho agent...", "confirmDeleteAgent": "Bạn có chắc chắn muốn xóa agent \"{{agentName}}\" không?", "deleteAgentWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "addNewAgent": "Thêm Agent mới", "selectAgentType": "<PERSON><PERSON><PERSON>", "selectAgent": "<PERSON><PERSON><PERSON> m<PERSON>t agent...", "customDescription": "<PERSON><PERSON> tả tùy chỉnh (tù<PERSON> chọn)", "enterCustomDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả tùy chỉnh cho agent này...", "useDefaultDescription": "<PERSON><PERSON><PERSON> để trống, sẽ sử dụng mô tả mặc định của lo<PERSON>i agent", "promptForAgent": "Prompt cho agent", "enterPrompt": "<PERSON><PERSON><PERSON><PERSON> prompt cho agent n<PERSON><PERSON>...", "required": "*"}, "agentCreatePage": {"title": "Tạo Agent", "selectAgentType": "<PERSON><PERSON><PERSON>", "agentTypeDescription": "Chọn loại agent phù hợp với nhu cầu của bạn. Mỗi loại agent có những khả năng và đặc điểm khác nhau.", "allTypes": "<PERSON><PERSON><PERSON> c<PERSON> lo<PERSON>i", "systemAgents": "System Agents", "userAgents": "User Agents", "sortByName": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> theo tên", "sortByDate": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> theo ng<PERSON>y", "ascending": "<PERSON><PERSON><PERSON>", "descending": "<PERSON><PERSON><PERSON><PERSON>", "resetFilters": "Đặt lại bộ lọc", "errorLoadingAgents": "<PERSON><PERSON> lỗi xảy ra khi tải danh sách Type Agent", "retry": "<PERSON><PERSON><PERSON> lại"}, "modelConfig": {"title": "<PERSON><PERSON><PERSON>", "provider": "Provider", "keyLlm": "Key LLM", "selectKeyLlm": "Chọn Key LLM", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "vectorStore": "Vector Store", "selectVectorStore": "Chọn vector store", "instruction": "Instruction", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho model...", "advancedSettings": "<PERSON><PERSON><PERSON> chỉnh nâng cao", "maxTokens": "<PERSON>", "maxTokensDescription": "Số lượng token tối đa cho mỗi lần gọi API", "temperature": "Temperature", "temperatureDescription": "<PERSON><PERSON><PERSON> độ ngẫu nhiên trong kết quả (0-2)", "topP": "Top P", "topPDescription": "<PERSON><PERSON><PERSON> su<PERSON>t tích lũy cho lựa chọn token (0-1)", "topK": "Top K", "topKDescription": "Số lượng token có xác suất cao nhất để xem xét", "loading": "<PERSON><PERSON> tả<PERSON>...", "errorLoadingModel": "Lỗi tải model", "noModel": "Không có model"}, "customToolConfig": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> các công cụ mà Agent có thể sử dụng", "availableTools": "<PERSON><PERSON>ng cụ có sẵn", "selectedTools": "<PERSON><PERSON>ng cụ đã chọn", "noToolsSelected": "<PERSON><PERSON><PERSON> chọn công cụ nào", "selectTools": "<PERSON><PERSON><PERSON> công cụ", "toolName": "<PERSON><PERSON><PERSON> công cụ", "toolDescription": "<PERSON><PERSON>", "addTool": "<PERSON><PERSON><PERSON><PERSON> công cụ", "removeTool": "<PERSON><PERSON><PERSON> công cụ"}, "toolSlideInForm": {"title": "<PERSON><PERSON><PERSON> công cụ", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "tool": "<PERSON><PERSON><PERSON> cụ", "description": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "name": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateToolsSuccess": "<PERSON><PERSON><PERSON> nhật công cụ thành công!", "updateToolsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật công cụ.", "addToolsToListSuccess": "<PERSON><PERSON> thêm công cụ vào danh sách!"}, "profileConfig": {"title": "Thông tin cá nhân", "name": "<PERSON><PERSON><PERSON>", "birthDate": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "language": "<PERSON><PERSON><PERSON>", "education": "<PERSON><PERSON><PERSON><PERSON> độ học vấn", "country": "Quốc gia", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "skills": "<PERSON><PERSON> n<PERSON>ng", "personality": "<PERSON><PERSON><PERSON>", "avatar": "Ảnh đại diện", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "highSchool": "<PERSON><PERSON> học", "college": "<PERSON> đẳng", "university": "<PERSON><PERSON><PERSON>", "postgraduate": "<PERSON><PERSON> <PERSON><PERSON><PERSON> học", "addSkill": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> n<PERSON>ng", "addPersonality": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> c<PERSON>ch", "skillPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> enter", "personalityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> enter", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên agent", "positionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "personalityTextPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả t<PERSON>h c<PERSON>ch"}, "agentConfigurationForm": {"createAgent": "Tạo Agent", "editAgent": "Chỉnh sửa Agent"}, "resourcesConfig": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> các tài nguyên mà Agent có thể sử dụng", "media": "Media", "urls": "URLs", "products": "Products", "addMedia": "Thêm Media", "addUrl": "Thêm URL", "addProduct": "Thêm Product", "noMediaSelected": "Chưa chọn media nào", "noUrlsSelected": "Chưa chọn URL nào", "noProductsSelected": "Chưa chọn product nào", "selectedItems": "{{count}} mục đ<PERSON> ch<PERSON>n"}, "urlSlideInForm": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "url": "URL", "description": "<PERSON><PERSON>", "category": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "lastChecked": "<PERSON><PERSON><PERSON> tra lần cu<PERSON>i", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "broken": "Lỗi", "pending": "<PERSON><PERSON> chờ", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateUrlsSuccess": "<PERSON><PERSON><PERSON> nhật URLs thành công!", "updateUrlsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật URLs.", "addUrlsToListSuccess": "<PERSON><PERSON> thêm URLs vào danh sách!"}, "productSlideInForm": {"title": "Chọn Products", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "name": "<PERSON><PERSON><PERSON>", "price": "Giá", "category": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "stock": "<PERSON><PERSON><PERSON> kho", "inStock": "<PERSON><PERSON><PERSON> hàng", "outOfStock": "<PERSON><PERSON><PERSON>", "discontinued": "<PERSON><PERSON><PERSON> kinh doanh", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateProductsSuccess": "<PERSON><PERSON><PERSON> nhật sản phẩm thành công!", "updateProductsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật sản phẩm.", "addProductsToListSuccess": "<PERSON><PERSON> thêm sản phẩm vào danh sách!"}, "agentEditPage": {"title": "Chỉnh sửa Agent", "loading": "<PERSON><PERSON> tải thông tin agent...", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent", "error": "<PERSON><PERSON> lỗi xảy ra khi tải thông tin agent"}, "zaloOfficialAccountConfig": {"title": "Zalo Official Account", "description": "<PERSON><PERSON><PERSON> các tài k<PERSON>n Zalo Official Account để tích hợp với agent c<PERSON><PERSON> bạn", "addAccount": "<PERSON><PERSON><PERSON><PERSON> tà<PERSON>", "zaloAccounts": "<PERSON><PERSON><PERSON>", "noAccountsSelected": "<PERSON><PERSON><PERSON> có tài khoản nào đ<PERSON><PERSON><PERSON> chọn", "selectedAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đã chọn", "accountCount": "{{count}} t<PERSON><PERSON>", "selectAccounts": "<PERSON><PERSON><PERSON> tà<PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>ếm tài k<PERSON>...", "oaId": "OA ID", "name": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "avatar": "Ảnh đại diện", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "deselectAll": "Bỏ chọn tất cả", "selected": "<PERSON><PERSON> ch<PERSON>n", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>...", "error": "<PERSON><PERSON> lỗi xảy ra khi tải danh sách tài kho<PERSON>n", "retry": "<PERSON><PERSON><PERSON> lại", "noAccountsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài kho<PERSON>n nào", "updateAccountsSuccess": "<PERSON><PERSON><PERSON> nhật tài k<PERSON>n <PERSON> thành công!", "updateAccountsError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật tài k<PERSON><PERSON>.", "addAccountsToListSuccess": "<PERSON><PERSON> thêm tài k<PERSON>n <PERSON> vào danh sách!", "removeAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "removeAccountSuccess": "Đã xóa tài khoản Zalo thành công!", "removeAccountError": "Có lỗi xảy ra khi xóa tài k<PERSON><PERSON>.", "confirmDeleteAccount": "<PERSON>ạn có chắc chắn muốn xóa tài k<PERSON><PERSON> \"{{accountName}}\" khỏi Agent không?", "deleteAccountWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác."}, "zaloSlideInForm": {"title": "<PERSON><PERSON><PERSON> Official Account", "close": "Đ<PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "createdAt": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "addAccountsToListSuccess": "<PERSON>ã thêm tài khoản <PERSON>alo thành công!", "updateAccountsSuccess": "<PERSON><PERSON><PERSON> nhật tài k<PERSON>n <PERSON> thành công!", "updateAccountsError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật tài k<PERSON><PERSON>.", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu ở chế độ này"}}}