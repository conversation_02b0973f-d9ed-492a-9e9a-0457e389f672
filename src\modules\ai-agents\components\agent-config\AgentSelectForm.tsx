import {  Card, Icon, IconCard, Select, Textarea, Typography } from '@/shared/components/common';
import React, { useState } from 'react';
import { MultiAgentItem } from '../../types/agent';

interface AgentSelectFormProps {
  /**
   * Danh sách các agent có thể chọn
   */
  availableAgents: MultiAgentItem[];

  /**
   * Callback khi thêm agent mới
   */
  onAddAgent?: (agent: MultiAgentItem) => void;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái hiển thị form
   */
  isVisible: boolean;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode của form (edit/create)
   */
  mode?: 'edit' | 'create';
}

/**
 * Component form để chọn và cấu hình agent mới
 */
const AgentSelectForm: React.FC<AgentSelectFormProps> = ({
  availableAgents,
  onAddAgent,
  onCancel,
  isVisible,
  agentId,
  mode = 'create',
}) => {
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [customDescription, setCustomDescription] = useState('');
  const [prompt, setPrompt] = useState('');

  // Reset form khi ẩn
  React.useEffect(() => {
    if (!isVisible) {
      setSelectedAgentId(null);
      setCustomDescription('');
      setPrompt('');
    }
  }, [isVisible]);

  // Lấy thông tin agent được chọn
  const selectedAgent = availableAgents.find(agent => String(agent.id) === selectedAgentId);

  // Xử lý thêm agent
  const handleAddAgent = async () => {
    if (!selectedAgent || !prompt.trim()) return;
    const newAgent: MultiAgentItem = {
      id: String(selectedAgent.id),
      agentTypeId: String(selectedAgent.id),
      name: selectedAgent.name,
      description: selectedAgent.description,
      avatar: selectedAgent.avatar ?? '',
      customDescription: customDescription.trim() || '',
      prompt: prompt.trim(),
    };
    if (mode === 'edit' && agentId) {
      // Gọi API thêm agent con vào multi-agent system
      try {
        const { addMultiAgent } = await import('../../api/multiAgent.api');
        await addMultiAgent(agentId, [{ agent_id: newAgent.id, prompt: newAgent.prompt }]);
        // Reset form
        setSelectedAgentId(null);
        setCustomDescription('');
        setPrompt('');
        if (onAddAgent) onAddAgent(newAgent); // callback để MultiAgentConfig refetch lại danh sách
        return;
      } catch (error) {
        console.error('Error adding multi agent:', error);
        return;
      }
    }
    // Mode create: logic cũ
    if (onAddAgent) {
      onAddAgent(newAgent);
    }
    setSelectedAgentId(null);
    setCustomDescription('');
    setPrompt('');
  };

  // Xử lý hủy
  const handleCancel = () => {
    setSelectedAgentId(null);
    setCustomDescription('');
    setPrompt('');
    if (onCancel) {
      onCancel();
    }
  };

  if (!isVisible) return null;

  return (
    <Card className="p-4 mb-4 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Icon name="plus" size="sm" className="text-blue-500" />
          <Typography variant="h6">Thêm Agent mới</Typography>
        </div>

        {/* Chọn loại agent */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Chọn loại Agent
          </label>
          <Select
            value={selectedAgentId || ''}
            onChange={(value) => setSelectedAgentId(typeof value === 'string' && value ? value : null)}
            placeholder="Chọn một agent..."
            className="w-full"
            options={availableAgents.map((agent) => ({
              value: agent.id,
              label: agent.name
            }))}
          />
        </div>

        {/* Hiển thị thông tin agent được chọn */}
        {selectedAgent && (
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Icon name={'user'} size="sm" className="text-white" />
              </div>
              <div className="flex-grow">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {selectedAgent.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {selectedAgent.description}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Mô tả tùy chỉnh */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mô tả tùy chỉnh (tùy chọn)
          </label>
          <Textarea
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
            placeholder="Nhập mô tả tùy chỉnh cho agent này..."
            className="w-full"
            rows={3}
          />
          <p className="text-xs text-gray-500 mt-1">
            Nếu để trống, sẽ sử dụng mô tả mặc định của loại agent
          </p>
        </div>

        {/* Prompt cho agent */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Prompt cho agent <span className="text-red-500">*</span>
          </label>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Nhập prompt cho agent này..."
            className="w-full"
            rows={2}
            required
          />
        </div>

        {/* Nút điều khiển */}
        <div className="flex gap-2 pt-2 justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title="Hủy"
            onClick={handleCancel}
          />
          <IconCard
            icon="plus"
            variant="primary"
            size="md"
            title="Thêm Agent"
            onClick={handleAddAgent}
            disabled={!selectedAgent || !prompt.trim()}
          />

          
        </div>
      </div>
    </Card>
  );
};

export default AgentSelectForm;
