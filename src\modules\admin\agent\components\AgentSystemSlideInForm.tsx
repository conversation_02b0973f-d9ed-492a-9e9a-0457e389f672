import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  Icon,
  IconCard,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { NotificationUtil } from '@/shared/utils/notification';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Interface cho Agent System
 */
export interface AgentSystem {
  id: string;
  name: string;
  description?: string;
  nameCode: string;
  model: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho query parameters
 */
interface AgentSystemQueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

/**
 * API function để lấy danh sách Agent Systems
 */
const getAgentSystems = async (
  params: AgentSystemQueryParams = {}
): Promise<ApiResponseDto<PaginatedResult<AgentSystem>>> => {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.search) queryParams.append('search', params.search);

  return apiClient.get(`/admin/agents/system?${queryParams.toString()}`);
};

/**
 * Props cho component AgentSystemSlideInForm
 */
interface AgentSystemSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * ID của agent type (cần thiết cho mode edit)
   */
  agentTypeId?: string | undefined;

  /**
   * Callback khi có Agent System được thêm/cập nhật
   */
  onSave?: (systemIds: string[]) => void;

  /**
   * Danh sách Agent System IDs đã chọn ban đầu
   */
  initialSelectedIds?: string[];
}

/**
 * Component form trượt để chọn các Agent Systems
 */
const AgentSystemSlideInForm: React.FC<AgentSystemSlideInFormProps> = ({
  isVisible,
  onClose,
  mode,
  agentTypeId,
  onSave,
  initialSelectedIds = [],
}) => {
    const { t } = useTranslation(['admin', 'common']);

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // Khởi tạo selectedIds từ initialSelectedIds
  useEffect(() => {
    if (initialSelectedIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedIds);
    }
  }, [initialSelectedIds, selectedIds.length]);

  // API hooks
  const { data: systemsResponse, isLoading } = useQuery({
    queryKey: ['agent-systems', page, limit, searchTerm],
    queryFn: () => getAgentSystems({
      page,
      limit,
      ...(searchTerm && { search: searchTerm })
    }),
    enabled: isVisible,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Lấy dữ liệu từ API response
  const systems: AgentSystem[] = systemsResponse?.result?.items || [];

  // Filter systems based on search term
  const filteredSystems = systems.filter(system =>
    system.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    system.nameCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Cấu hình cột cho bảng
  const columns: TableColumn<AgentSystem>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'system',
      title: t('admin:agent.form.agentSystemSlideIn.system'),
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          {/* Icon */}
          <div className="w-8 h-8 rounded-full overflow-hidden mr-3 bg-gradient-to-br from-blue-500 to-purple-600 flex-shrink-0 flex items-center justify-center">
            <Icon name="settings" size="sm" className="text-white" />
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.description?.slice(0, 100)}{record.description && record.description.length > 100 && '...'}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'nameCode',
      title: 'Code',
      dataIndex: 'nameCode',
      width: '20%',
      render: (_, record) => (
        <Typography variant="body2" className="font-mono text-sm">
          {record.nameCode}
        </Typography>
      ),
    },
   {
      key: 'model',
      title: 'model',
      dataIndex: 'model',
      width: '20%',
      render: (_, record) => (
        <Typography variant="body2" className="font-mono text-sm">
          {record.model}
        </Typography>
      ),
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentTypeId) {
        NotificationUtil.error({
          message: t('admin:agent.form.agentSystemSlideIn.cannotSaveInThisMode'),
        });
        return;
      }

      setIsSubmitting(true);
      try {
        // Mode edit: trả về danh sách system IDs đã chọn
        if (onSave) {
          onSave(selectedIds);
        }

        NotificationUtil.success({
          message: t('admin:agent.form.agentSystemSlideIn.updateSystemsSuccess'),
        });

        onClose();
      } catch {
        NotificationUtil.error({
          message: t('admin:agent.form.agentSystemSlideIn.updateSystemsError'),
        });
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách system IDs đã chọn
      if (onSave) {
        onSave(selectedIds);
      }

      NotificationUtil.success({
        message: t('admin:agent.form.agentSystemSlideIn.addSystemsToListSuccess'),
      });

      onClose();
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    setSearchTerm('');
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'filter-all',
      label: t('admin:agent.form.agentSystemSlideIn.filterBy'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'all',
      label: t('admin:agent.form.agentSystemSlideIn.all'),
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('admin:agent.form.agentSystemSlideIn.title')}</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            {t('admin:agent.form.agentSystemSlideIn.close')}
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<AgentSystem>
            columns={columns}
            data={filteredSystems}
            rowKey="id"
            loading={isLoading}
            sortable={false}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: page,
              pageSize: limit,
              total: filteredSystems.length,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
              onChange: (newPage) => setPage(newPage),
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('admin:agent.form.agentSystemSlideIn.cancel')}
            onClick={handleClose}
            disabled={isSubmitting}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('admin:agent.form.agentSystemSlideIn.save')}
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || isSubmitting || (mode === 'edit' && !agentTypeId) || selectedIds.length === 0}
          />
        </div>
      </Card>
    </SlideInForm>
  );
};

export default AgentSystemSlideInForm;
