# Tools Module Internationalization

This directory contains internationalization (i18n) files for the Tools module.

## Structure

```
locales/
├── vi.json     # Vietnamese translations
├── en.json     # English translations  
├── zh.json     # Chinese translations
├── index.ts    # Export configuration
└── README.md   # This file
```

## Usage

The tools module uses the `tools` namespace for all translations. To use translations in components:

```typescript
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation(['tools', 'common']);
  
  return (
    <div>
      <h1>{t('tools:title', 'Tools')}</h1>
      <p>{t('tools:description', 'Manage your tools')}</p>
    </div>
  );
};
```

## Translation Keys Structure

### Main Categories

- `tools:title` - Module title
- `tools:description` - Module description
- `tools:status.*` - Tool status translations
- `tools:form.*` - Form field labels and placeholders
- `tools:actions.*` - Action button labels
- `tools:messages.*` - Success/error messages
- `tools:validation.*` - Form validation messages
- `tools:table.*` - Table column headers
- `tools:filters.*` - Filter options
- `tools:versions.*` - Version management
- `tools:integration.*` - Integration related translations
- `tools:management.*` - Tool management features

### Integration Specific

- `tools:integration.title` - Integration page title
- `tools:integration.auth.*` - Authentication types
- `tools:integration.table.*` - Integration table columns
- `tools:integration.validation.*` - Integration form validation

## Adding New Translations

1. Add the key to all language files (vi.json, en.json, zh.json)
2. Use the `tools:` namespace prefix
3. Follow the existing structure and naming conventions
4. Test the translation in the UI

## Best Practices

1. **Namespace Usage**: Always use `tools:` prefix for this module
2. **Fallback Values**: Provide fallback text as second parameter to `t()`
3. **Consistent Structure**: Follow the established key hierarchy
4. **Descriptive Keys**: Use clear, descriptive key names
5. **Pluralization**: Use i18next pluralization features when needed

## Examples

```typescript
// Basic usage
t('tools:title', 'Tools')

// Nested keys
t('tools:form.name', 'Tool Name')
t('tools:status.approved', 'Approved')

// With interpolation
t('tools:messages.createSuccess', 'Tool {{name}} created successfully!', { name: toolName })

// Multiple namespaces
const { t } = useTranslation(['tools', 'common']);
t('tools:title', 'Tools')
t('common:save', 'Save')
```

## Maintenance

- Keep all language files synchronized
- Update translations when adding new features
- Review translations for consistency and accuracy
- Test with different languages to ensure UI layout works properly
