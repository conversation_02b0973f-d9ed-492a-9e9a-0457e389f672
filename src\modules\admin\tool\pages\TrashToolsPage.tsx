import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Pagination } from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import TrashToolGrid from '../components/TrashToolGrid';

import { useDeletedAdminTools, useRollbackAdminTool } from '@/modules/admin/tool/hooks/useTool';
import { ToolListItem, ToolSortBy } from '@/modules/admin/tool/types/tool.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ToolQueryParams } from '@/modules/tools/types';

/**
 * Trang quản lý Tool đã xóa mềm
 */
const TrashToolsPage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['admin-tool', 'common']);
  const { success, error } = useSmartNotification();

  // State cho tìm kiếm và phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy] = useState<ToolSortBy>(ToolSortBy.CREATED_AT);
  const [sortDirection] = useState<SortDirection>(SortDirection.DESC);

  // API hooks
  const { mutateAsync: rollbackTool } = useRollbackAdminTool();

  // Xử lý khôi phục tool
  const handleRollback = useCallback(
    async (tool: ToolListItem) => {
      try {
        await rollbackTool(tool.id);
        success({
          message: t('admin-tool:trash.rollbackSuccess', 'Khôi phục tool thành công'),
          duration: 3000,
        });
      } catch (err) {
        console.error('Error rolling back tool:', err);
        error({
          message: t('admin-tool:trash.rollbackError', 'Lỗi khi khôi phục tool'),
          duration: 3000,
        });
      }
    },
    [rollbackTool, t, success, error]
  );

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    const params: ToolQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      sortDirection: sortDirection,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (sortBy) {
      params.sortBy = sortBy;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // API hooks
  const { data: toolsData, isLoading } = useDeletedAdminTools(queryParams);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1
  }, []);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  return (
    <div className="space-y-6">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('admin-tool:filters.all', 'Tất cả'),
            icon: 'list',
            onClick: () => '',
          },
        ]}
        showDateFilter={false}
        showColumnFilter={false}
        // Không có onAdd prop để ẩn nút thêm mới cho trang trash
      />

      {/* Tools Grid */}
      <TrashToolGrid
        tools={toolsData?.items || []}
        loading={isLoading}
        onRestore={handleRollback}
      />

      {/* Pagination - Hiển thị khi có dữ liệu */}
      {toolsData?.items && toolsData.items.length > 0 && (
        <div className="flex justify-end mt-8">
          <Pagination
            variant="simple"
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            totalPages={toolsData.meta?.totalPages || 1}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            showFirstLastButtons={false}
            showItemsPerPageSelector={true}
            showPageInfo={false}
            itemsPerPageOptions={[10, 20, 50, 100]}
            size="md"
            borderless={true}
          />
        </div>
      )}
    </div>
  );
};

export default TrashToolsPage;
