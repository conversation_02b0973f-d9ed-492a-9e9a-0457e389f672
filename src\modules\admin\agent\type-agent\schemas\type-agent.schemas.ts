import { z } from 'zod';
import { TypeAgentSortBy, SortDirection, AgentTypeStatusEnum } from '../types/type-agent.types';

/**
 * Schema cho cấu hình mặc định
 */
export const defaultConfigSchema = z.object({
  enableAgentProfileCustomization: z.boolean(),
  enableOutputToMessenger: z.boolean(),
  enableOutputToWebsiteLiveChat: z.boolean(),
  enableTaskConversionTracking: z.boolean(),
  enableResourceUsage: z.boolean(),
  enableDynamicStrategyExecution: z.boolean(),
  enableMultiAgentCollaboration: z.boolean(),
  enableOutputToZaloOA: z.boolean(),
});

/**
 * Schema cho tạo type agent
 */
export const createTypeAgentSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên loại agent là bắt buộc')
    .max(255, 'Tên loại agent không được quá 255 ký tự'),
  description: z
    .string()
    .min(1, 'Mô tả là bắt buộc')
    .max(500, '<PERSON>ô tả không được quá 500 ký tự'),
  defaultConfig: defaultConfigSchema,
  status: z.nativeEnum(AgentTypeStatusEnum),
  agentSystems: z.array(z.string()).min(1, 'Ít nhất một agent system là bắt buộc'),
});

/**
 * Schema cho cập nhật type agent
 */
export const updateTypeAgentSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên loại agent là bắt buộc')
    .max(255, 'Tên loại agent không được quá 255 ký tự')
    .optional(),
  description: z
    .string()
    .min(1, 'Mô tả là bắt buộc')
    .max(500, 'Mô tả không được quá 500 ký tự')
    .optional(),
  defaultConfig: defaultConfigSchema.optional(),
  status: z.nativeEnum(AgentTypeStatusEnum).optional(),
  agentSystems: z.array(z.string()).min(1, 'Ít nhất một agent system là bắt buộc').optional(),
});

/**
 * Schema cho query parameters
 */
export const typeAgentQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(AgentTypeStatusEnum).optional(),
  sortBy: z.nativeEnum(TypeAgentSortBy).optional().default(TypeAgentSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho type agent list item
 */
export const typeAgentListItemSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  createdAt: z.string(),
  status: z.nativeEnum(AgentTypeStatusEnum),
});

/**
 * Schema cho type agent detail
 */
export const typeAgentDetailSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  defaultConfig: defaultConfigSchema,
  status: z.nativeEnum(AgentTypeStatusEnum),
  agentSystems: z.array(z.string()),
});

/**
 * Schema cho response API
 */
export const typeAgentResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: z.object({
    items: z.array(typeAgentListItemSchema),
    meta: z.object({
      totalItems: z.number(),
      itemCount: z.number(),
      itemsPerPage: z.number(),
      totalPages: z.number(),
      currentPage: z.number(),
    }),
  }),
});

export type CreateTypeAgentFormData = z.infer<typeof createTypeAgentSchema>;
export type UpdateTypeAgentFormData = z.infer<typeof updateTypeAgentSchema>;
export type TypeAgentQueryFormData = z.infer<typeof typeAgentQuerySchema>;
