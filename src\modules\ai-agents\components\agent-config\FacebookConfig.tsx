import { Button, Icon, Modal, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import React, { useState, useEffect, useRef } from 'react';
import FacebookSlideInForm from './FacebookSlideInForm';
import {
  useAgentFacebookPages,
  useRemoveFacebookPage,
} from '../../hooks/useAgentIntegration';
import { NotificationUtil } from '@/shared/utils/notification';

import { IntegrationItem, IntegrationsData } from '../../types/integration';
import { TypeAgentConfig } from '../../types/dto';

interface FacebookConfigProps {
  agentId?: string;
  initialData?: IntegrationsData;
  onSave?: (data: IntegrationsData) => void;
  mode?: 'create' | 'edit';
  typeAgentConfig?: TypeAgentConfig; // Type agent config để kiểm tra capabilities
}

/**
 * Component hiển thị một tích hợp Facebook
 */
const FacebookItemCard: React.FC<{
  item: IntegrationItem;
  onRemove: (id: string) => void;
}> = ({ item, onRemove }) => {
  const { t } = useTranslation(['common', 'aiAgents']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  return (
    <div
      className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm"
    >
      {/* Icon/Avatar */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        {item.icon ? (
          <img src={item.icon} alt={item.name} className="w-full h-full object-cover" />
        ) : (
          <Icon
            name="facebook"
            size="md"
            className="text-blue-600"
          />
        )}
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {item.name}
        </h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
          <Icon name="document" size="sm" className="mr-1" />
          <span className="truncate">{item.id}</span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => {
          onRemove(item.id);
        }}
        aria-label="Xóa tích hợp Facebook"
      >
        <Icon name="trash" size="sm" />
      </Button>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={() => {
                onRemove(item.id);
                setShowDeleteModal(false);
              }}
            >
              {t('common:delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'aiAgents:integration.confirmDeleteIntegration',
              'Bạn có chắc chắn muốn xóa tích hợp "{{integrationName}}" khỏi Agent không?',
              { integrationName: item.name }
            )}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            {t(
              'aiAgents:integration.deleteIntegrationWarning',
              'Hành động này không thể hoàn tác.'
            )}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

/**
 * Component cấu hình Facebook cho Agent
 */
const FacebookConfig: React.FC<FacebookConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create',
  typeAgentConfig
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);

  // State cho dữ liệu tích hợp
  const [configData, setConfigData] = useState<IntegrationsData>(
    initialData || {
      integrations: [],
    }
  );

  // API hooks để lấy dữ liệu từ agent - chỉ gọi khi type config cho phép
  const shouldLoadFacebookPages = mode === 'edit' && agentId && typeAgentConfig?.enableOutputToMessenger;

  const {
    data: facebookPagesResponse,
    isLoading: isLoadingFacebook,
    error: facebookError,
    refetch: refetchFacebookPages,
  } = useAgentFacebookPages(shouldLoadFacebookPages ? agentId : '');

  // Mutation hooks
  const removeFacebookPageMutation = useRemoveFacebookPage();

  // State cho form slide-in
  const [showFacebookForm, setShowFacebookForm] = useState(false);

  // Sử dụng ref để track việc đã initialize chưa
  const isInitialized = useRef(false);

  // Cập nhật dữ liệu từ API
  useEffect(() => {
    console.log('🔄 FacebookConfig useEffect triggered:', {
      mode,
      agentId,
      facebookPagesResponse,
      initialData,
      isInitialized: isInitialized.current
    });

    if (mode === 'edit' && agentId && typeAgentConfig?.enableOutputToMessenger) {
      const integrations: IntegrationItem[] = [];

      // Thêm Facebook pages từ API
      if (facebookPagesResponse?.facebookPages) {
        const facebookIntegrations = facebookPagesResponse.facebookPages.map(page => ({
          id: page.id,
          name: page.pageName,
          type: 'facebook' as const,
          icon: page.avatarPage || undefined,
          isConnected: true,
          status: 'active' as const,
        }));
        integrations.push(...facebookIntegrations);
      } else {
        console.log('📘 No Facebook pages found in response');
      }

      setConfigData({ integrations });
      isInitialized.current = true;
    } else if (!initialData && !isInitialized.current) {
      // Create mode - empty state, chỉ set lần đầu tiên
      setConfigData({ integrations: [] });
      isInitialized.current = true;
    }
  }, [facebookPagesResponse, mode, agentId, typeAgentConfig?.enableOutputToMessenger, initialData]);

  // Lọc các tích hợp Facebook
  const facebookIntegrations = configData.integrations.filter(item => item.type === 'facebook');



  // Xử lý xóa Facebook Page với API call
  const handleRemoveFacebookPageWithAPI = async (pageId: string) => {
    if (!agentId || mode !== 'edit') return;

    try {
      await removeFacebookPageMutation.mutateAsync({
        agentId,
        pageId,
      });

      // Force refetch data để cập nhật giao diện ngay lập tức
      await refetchFacebookPages();

      NotificationUtil.success({
        message: t('aiAgents:integration.removeFacebookSuccess'),
      });
    } catch {
      NotificationUtil.error({
        message: t('aiAgents:integration.removeFacebookError'),
      });
    }
  };

  // Xử lý khi xóa một tích hợp - phân biệt theo mode
  const handleRemoveIntegration = (id: string) => {
    const item = configData.integrations.find(integration => integration.id === id);
    if (!item) return;

    if (mode === 'edit') {
      // Edit mode: gọi API
      if (item.type === 'facebook') {
        handleRemoveFacebookPageWithAPI(id);
      }
    } else {
      // Create mode: chỉ cập nhật local state
      const updatedIntegrations = configData.integrations.filter(
        integration => integration.id !== id
      );
      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
  };

  // Xử lý khi thêm một tích hợp mới
  const handleAddIntegration = () => {
    setShowFacebookForm(true);
  };

  // Callback để nhận dữ liệu từ FacebookSlideInForm
  const handleFacebookFormSave = async (selectedIds: string[]) => {
    if (mode === 'create') {
      // Create mode: cập nhật local state
      const newFacebookIntegrations = selectedIds.map(id => ({
        id,
        name: `Facebook Page ${id}`,
        type: 'facebook' as const,
        isConnected: true,
        status: 'active' as const,
      }));

      const updatedIntegrations = [
        ...configData.integrations.filter(item => item.type !== 'facebook'),
        ...newFacebookIntegrations,
      ];

      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    } else {
      // Edit mode: force refetch data để cập nhật giao diện ngay lập tức
      try {
        await refetchFacebookPages();
        console.log('Facebook pages data refetched successfully');
      } catch (error) {
        console.error('Error refetching Facebook pages:', error);
      }
    }
    setShowFacebookForm(false);
  };

  return (
    <>
      <ConfigComponentWrapper
        componentId="facebook"
        title={
          <div className="flex items-center">
            <Icon name="facebook" size="md" className="mr-2 text-blue-600" />
            <span>{t('aiAgents:integration.facebookIntegration')}</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('aiAgents:integration.connectAgentToFacebook')}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('aiAgents:integration.facebookDescription')}
            </p>
          </div>

          {/* Tích hợp Facebook */}
          <div>
            <div className="flex items-center mb-3">
              <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                <Icon name="facebook" size="sm" className="text-white" />
              </div>
              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                {t('aiAgents:integration.facebookPages')}
              </h3>
            </div>

            <div className="space-y-3">
              {isLoadingFacebook ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  {t('aiAgents:mediaSlideInForm.loadingFacebookPages')}
                </div>
              ) : facebookError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  {t('aiAgents:mediaSlideInForm.errorLoadingFacebookPages')}
                </div>
              ) : facebookIntegrations.length > 0 ? (
                <div className={`space-y-3 ${facebookIntegrations.length > 10 ? 'max-h-96 overflow-y-auto pr-2' : ''}`}>
                  {facebookIntegrations.map(item => (
                    <FacebookItemCard
                      key={item.id}
                      item={item}
                      onRemove={handleRemoveIntegration}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  {t('aiAgents:mediaSlideInForm.noFacebookIntegrations')}
                </div>
              )}
            </div>

            {/* Nút thêm Facebook */}
            {!showFacebookForm && (
              <div className='flex justify-center pt-6'>
                <Button variant="outline" size="sm" onClick={handleAddIntegration}>
                  <Icon name="plus" size="sm" className="mr-1" />
                  {t('aiAgents:integration.add')}
                </Button>
              </div>
            )}
          </div>

          {/* Form slide-in cho Facebook */}
          <FacebookSlideInForm
            isVisible={showFacebookForm}
            onClose={() => setShowFacebookForm(false)}
            onSave={handleFacebookFormSave}
            agentId={agentId ?? ''} // Nếu undefined, truyền chuỗi rỗng thay thế
            mode={mode}
          />
        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default FacebookConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { FacebookConfigProps };
