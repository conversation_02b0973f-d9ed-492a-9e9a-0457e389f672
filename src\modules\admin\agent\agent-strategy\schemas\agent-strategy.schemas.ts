import { z } from 'zod';
import { AgentStrategySortBy, SortDirection } from '../types/agent-strategy.types';

/**
 * Schema cho cấu hình model AI
 */
export const modelConfigSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().min(1).optional(),
});

/**
 * Schema cho nội dung bước
 */
export const strategyContentSchema = z.object({
  stepOrder: z.number().min(1),
  content: z.string().min(1, 'Nội dung bước là bắt buộc'),
});

/**
 * Schema cho ví dụ mặc định
 */
export const strategyExampleSchema = z.object({
  stepOrder: z.number().min(1),
  content: z.string().min(1, 'Nội dung ví dụ là bắt buộc'),
});

/**
 * Schema cho tạo agent strategy
 */
export const createAgentStrategySchema = z.object({
  name: z.string().min(1, 'Tên strategy là bắt buộc').max(255, 'Tên strategy không được quá 255 ký tự'),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema,
  instruction: z.string().min(1, 'Hướng dẫn là bắt buộc'),
  vectorStoreId: z.string().optional().nullable(),
  content: z.array(strategyContentSchema).min(1, 'Phải có ít nhất 1 bước'),
  exampleDefault: z.array(strategyExampleSchema).min(1, 'Phải có ít nhất 1 ví dụ'),
  systemModelId: z.string().uuid('System Model ID phải là UUID hợp lệ'),
});

/**
 * Schema cho cập nhật agent strategy
 */
export const updateAgentStrategySchema = z.object({
  name: z.string().min(1, 'Tên strategy là bắt buộc').max(255, 'Tên strategy không được quá 255 ký tự').optional(),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema.optional(),
  instruction: z.string().min(1, 'Hướng dẫn là bắt buộc').optional(),
  vectorStoreId: z.string().optional().nullable(),
  content: z.array(strategyContentSchema).min(1, 'Phải có ít nhất 1 bước').optional(),
  exampleDefault: z.array(strategyExampleSchema).min(1, 'Phải có ít nhất 1 ví dụ').optional(),
  systemModelId: z.string().uuid('System Model ID phải là UUID hợp lệ').optional(),
});

/**
 * Schema cho query parameters
 */
export const agentStrategyQuerySchema = z.object({
  search: z.string().optional(),
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  sortBy: z.nativeEnum(AgentStrategySortBy).optional().default(AgentStrategySortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho agent strategy list item
 */
export const agentStrategyListItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  systemModelId: z.string(),
  modelId: z.string(),
});

/**
 * Schema cho agent strategy detail
 */
export const agentStrategyDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  modelConfig: modelConfigSchema,
  instruction: z.string(),
  vectorStoreId: z.string().nullable(),
  content: z.array(strategyContentSchema),
  exampleDefault: z.array(strategyExampleSchema),
  systemModelId: z.string(),
});

/**
 * Schema cho response tạo agent strategy
 */
export const createAgentStrategyResponseSchema = z.object({
  id: z.string(),
  avatarUrlUpload: z.string().optional(),
});

/**
 * Schema cho response cập nhật agent strategy
 */
export const updateAgentStrategyResponseSchema = z.object({
  avatarUrlUpload: z.string().optional(),
});

/**
 * Schema cho response danh sách agent strategy
 */
export const agentStrategyListResponseSchema = z.object({
  items: z.array(agentStrategyListItemSchema),
  meta: z.object({
    totalItems: z.number(),
    itemCount: z.number(),
    itemsPerPage: z.number(),
    totalPages: z.number(),
    currentPage: z.number(),
    hasItems: z.boolean(),
  }),
});
