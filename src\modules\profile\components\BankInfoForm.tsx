import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Icon,
  Typography,
  Loading,
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import type { SelectOption } from '@/shared/components/common/Select/Select';
import { FormStatus } from '../types/profile.types';
import { useBankInfo, useUpdateBankInfo } from '../hooks/useUser';
import { getBanks } from '../services/user.service';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useProfileAccordion } from '../hooks/useProfileAccordion';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { BankQueryDto } from '../types/user.types';
import { createBankInfoSchema, BankInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';

/**
 * Component form thông tin ngân hàng
 */
const BankInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const { showNotification } = useSmartNotification();
  const { isCardOpen } = useProfileAccordion();

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const bankInfoSchema = createBankInfoSchema(t);

  // Kiểm tra xem card có đang mở không để lazy load
  const isCardOpened = isCardOpen(PROFILE_CARD_IDS.BANK_INFO);

  // Sử dụng hooks để lấy và cập nhật dữ liệu - chỉ khi card được mở
  const {
    data: bankInfo,
    isLoading: isLoadingBankInfo,
    error: bankInfoError,
  } = useBankInfo({
    enabled: isCardOpened,
  });
  const updateBankInfoMutation = useUpdateBankInfo();

  // Hàm load options cho AsyncSelectWithPagination
  const loadBankOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      const bankQuery: BankQueryDto = {
        page: params.page || 1,
        limit: params.limit || 20,
        isActive: true,
        search: params.search,
      };

      const banksData = await getBanks(bankQuery);

      // Transform data to SelectOption format
      const items = Array.isArray(banksData)
        ? banksData.map((bank: { bankCode: string; bankName: string; logoPath: string }) => {
            // Lưu logo vào localStorage để đảm bảo nó luôn có sẵn
            if (bank.logoPath && bank.bankCode) {
              try {
                localStorage.setItem(`select-option-image-${bank.bankCode}`, bank.logoPath);
              } catch (e) {
                console.error('Failed to save bank logo to localStorage:', e);
              }
            }

            return {
              value: bank.bankCode,
              label: bank.bankName,
              data: { image: bank.logoPath },
            };
          })
        : [];

      // Simulate pagination (since API doesn't return pagination info)
      const totalItems = items.length;
      const totalPages = Math.ceil(totalItems / (params.limit || 20));

      return {
        items,
        totalItems,
        totalPages,
        currentPage: params.page || 1,
      };
    } catch (error) {
      console.error('Error loading banks:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  // Xử lý khi submit form
  const onSubmit = (data: BankInfoSchema) => {
    // Sử dụng dữ liệu từ form
    const submittingData: BankInfoSchema = data;

    // Kiểm tra dữ liệu trước khi gửi
    if (!submittingData.bankCode) {
      showNotification('error', t('profile:validation.bankCodeRequired'));
      return;
    }

    if (!submittingData.accountNumber) {
      showNotification('error', t('profile:validation.accountNumberRequired'));
      return;
    }

    if (!submittingData.accountHolder) {
      showNotification('error', t('profile:validation.accountHolderRequired'));
      return;
    }

    // Cập nhật trạng thái form
    setFormStatus(FormStatus.SUBMITTING);

    // Gọi mutation để cập nhật thông tin ngân hàng
    updateBankInfoMutation.mutate(
      {
        bankCode: submittingData.bankCode,
        accountNumber: submittingData.accountNumber,
        accountHolder: submittingData.accountHolder,
        ...(submittingData.bankBranch && { bankBranch: submittingData.bankBranch }),
      },
      {
        onSuccess: () => {
          // Cập nhật trạng thái form
          setFormStatus(FormStatus.IDLE);

          // Hiển thị thông báo thành công
          showNotification('success', t('profile:messages.updateSuccess'));
        },
        onError: error => {
          // Cập nhật trạng thái form
          setFormStatus(FormStatus.IDLE);
          // Hiển thị thông báo lỗi
          let errorMessage = t('profile:messages.updateError');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as { response?: { data?: { message?: string } } };
            if (axiosError.response?.data?.message) {
              errorMessage = axiosError.response.data.message;
            }
          }
          showNotification('error', errorMessage);
        },
      }
    );
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Ngăn chặn sự kiện mặc định (submit form)
    e.preventDefault();
    e.stopPropagation();

    // Reset form về giá trị ban đầu từ bankInfo data
    // Sẽ được implement sau khi có form ref
  };

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="document" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:bankInfo.title')}
      </Typography>
    </div>
  );

  // Nếu card chưa được mở, chỉ hiển thị card rỗng
  if (!isCardOpened) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BANK_INFO} title={cardTitle}>
        <div></div>
      </ProfileCard>
    );
  }

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingBankInfo) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BANK_INFO} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (bankInfoError) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.BANK_INFO} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingBankInfo')}</div>
      </ProfileCard>
    );
  }

  // Render option cho AsyncSelectWithPagination
  const renderBankOption = (option: SelectOption) => {
    // Lấy URL hình ảnh
    let imageUrl = option.imageUrl;

    // Nếu không có imageUrl trực tiếp, kiểm tra trong data
    if (!imageUrl && option.data && typeof option.data === 'object') {
      if ('imageUrl' in option.data) {
        imageUrl = String(option.data['imageUrl']);
      } else if ('image' in option.data) {
        imageUrl = String(option.data['image']);
      }
    }

    // Lấy URL hình ảnh từ localStorage nếu không có trong data
    const cachedImageUrl = !imageUrl
      ? localStorage.getItem(`select-option-image-${option.value}`)
      : undefined;

    // Sử dụng URL hình ảnh từ data hoặc từ localStorage
    const finalImageUrl = imageUrl || cachedImageUrl;

    return (
      <div className="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
        {finalImageUrl ? (
          <div className="w-10 h-10 mr-2.5 flex-shrink-0 border border-border/30 rounded-sm overflow-hidden bg-white flex items-center justify-center">
            <img
              src={finalImageUrl}
              alt={option.label}
              className="w-8 h-8 object-contain"
              loading="eager"
              onError={e => {
                // Fallback if image fails to load
                (e.target as HTMLImageElement).style.display = 'none';
                // Hiển thị fallback khi lỗi
                const parent = (e.target as HTMLImageElement).parentElement;
                if (parent) {
                  parent.innerHTML = `<span class="text-xs font-bold text-primary">${option.label.charAt(0)}</span>`;
                  parent.classList.add('bg-primary/10');
                }
              }}
            />
          </div>
        ) : (
          // Fallback icon if no image
          <div className="w-6 h-6 mr-2.5 flex-shrink-0 bg-primary/10 rounded-sm flex items-center justify-center">
            <span className="text-xs font-bold text-primary">{option.label.charAt(0)}</span>
          </div>
        )}
        <span className="truncate font-medium">{option.label}</span>
      </div>
    );
  };

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.BANK_INFO} title={cardTitle}>
      <Form
        schema={bankInfoSchema}
        defaultValues={{
          bankCode: bankInfo?.bankCode || '',
          accountNumber: bankInfo?.accountNumber || '',
          accountHolder: bankInfo?.accountHolder || '',
          bankBranch: bankInfo?.bankBranch || '',
        }}
        onSubmit={data => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            return;
          }

          onSubmit(data as BankInfoSchema);
        }}
      >
        <div className="space-y-6">
          {/* Tên ngân hàng */}
          <div>
            <FormItem name="bankCode" label={t('profile:bankInfo.bankName')}>
              <AsyncSelectWithPagination
                loadOptions={loadBankOptions}
                placeholder={t('profile:banksData.bankName')}
                fullWidth
                renderOption={renderBankOption}
                itemsPerPage={20}
                autoLoadInitial={true}
                searchOnEnter={false}
                debounceTime={300}
                noOptionsMessage={t('profile:banksData.noResults', 'Không tìm thấy ngân hàng')}
                loadingMessage={t('common:loading', 'Đang tải...')}
              />
            </FormItem>
          </div>

          {/* Số tài khoản */}
          <div>
            <FormItem name="accountNumber" label={t('profile:bankInfo.accountNumber')}>
              <Input
                placeholder={t('profile:bankInfo.accountNumber')}
                fullWidth
                inputMode="numeric"
                pattern="[0-9]*"
                onInput={e => {
                  const input = e.currentTarget;
                  input.value = input.value.replace(/\D/g, ''); // xoá mọi ký tự không phải số
                }}
              />
            </FormItem>
          </div>

          {/* Chi nhánh */}
          <div>
            <FormItem name="bankBranch" label={t('profile:bankInfo.branch')}>
              <Input placeholder={t('profile:bankInfo.branch')} fullWidth />
            </FormItem>
          </div>

          {/* Chủ tài khoản */}
          <div>
            <FormItem name="accountHolder" label={t('profile:bankInfo.accountHolder')}>
              <Input placeholder={t('profile:bankInfo.accountHolder')} fullWidth />
            </FormItem>
          </div>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.cancel')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.save')}
            </Button>
          </div>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default BankInfoForm;
