import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho notification options
 */
export interface AdminAgentNotificationOptions {
  title?: string;
  message: string;
  duration?: number;
}

/**
 * Hook để hiển thị notification với đa ngôn ngữ cho Admin Agent module
 */
export const useAdminAgentNotification = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { success, error: showError, warning, info } = useSmartNotification();

  return {
    /**
     * Hiển thị thông báo thành công
     */
    success: (options: AdminAgentNotificationOptions) => {
      success({
        title: options.title || t('common:success', 'Thành công'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * Hiển thị thông báo lỗi
     */
    error: (options: AdminAgentNotificationOptions) => {
      showError({
        title: options.title || t('common:error', 'Lỗi'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * Hiển thị thông báo cảnh báo
     */
    warning: (options: AdminAgentNotificationOptions) => {
      warning({
        title: options.title || t('common:warning', 'Cảnh báo'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * Hiển thị thông báo thông tin
     */
    info: (options: AdminAgentNotificationOptions) => {
      info({
        title: options.title || t('common:info', 'Thông tin'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    // Specific notification methods for common admin agent operations
    
    /**
     * Thông báo tạo thành công
     */
    createSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('admin:agent.notification.createSuccess', '{{entityName}} đã được tạo thành công', { entityName }),
      });
    },

    /**
     * Thông báo cập nhật thành công
     */
    updateSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('admin:agent.notification.updateSuccess', '{{entityName}} đã được cập nhật thành công', { entityName }),
      });
    },

    /**
     * Thông báo xóa thành công
     */
    deleteSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('admin:agent.notification.deleteSuccess', '{{entityName}} đã được xóa thành công', { entityName }),
      });
    },

    /**
     * Thông báo khôi phục thành công
     */
    restoreSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('admin:agent.notification.restoreSuccess', '{{entityName}} đã được khôi phục thành công', { entityName }),
      });
    },

    /**
     * Thông báo lỗi tạo
     */
    createError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.createError', 'Có lỗi xảy ra khi tạo {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi cập nhật
     */
    updateError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.updateError', 'Có lỗi xảy ra khi cập nhật {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi xóa
     */
    deleteError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.deleteError', 'Có lỗi xảy ra khi xóa {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi khôi phục
     */
    restoreError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.restoreError', 'Có lỗi xảy ra khi khôi phục {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi tải dữ liệu
     */
    loadError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.loadError', 'Không thể tải danh sách {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo upload thành công
     */
    uploadSuccess: (fileName?: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: fileName 
          ? t('admin:agent.notification.uploadSuccessWithName', 'Tải lên {{fileName}} thành công', { fileName })
          : t('admin:agent.notification.uploadSuccess', 'Tải lên thành công'),
      });
    },

    /**
     * Thông báo upload lỗi
     */
    uploadError: (errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('admin:agent.notification.uploadError', 'Có lỗi xảy ra khi tải lên'),
      });
    },

    /**
     * Thông báo validation lỗi
     */
    validationError: (message: string) => {
      showError({
        title: t('admin:agent.notification.validationError', 'Dữ liệu không hợp lệ'),
        message,
      });
    },

    /**
     * Thông báo không có quyền
     */
    permissionError: () => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: t('admin:agent.notification.permissionError', 'Bạn không có quyền thực hiện thao tác này'),
      });
    },

    /**
     * Thông báo network error
     */
    networkError: () => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: t('admin:agent.notification.networkError', 'Lỗi kết nối mạng. Vui lòng thử lại'),
      });
    },

    /**
     * Thông báo đang xử lý
     */
    processing: (action: string) => {
      info({
        title: t('common:info', 'Thông tin'),
        message: t('admin:agent.notification.processing', 'Đang {{action}}...', { action }),
        duration: 2000,
      });
    },
  };
};

export default useAdminAgentNotification;
