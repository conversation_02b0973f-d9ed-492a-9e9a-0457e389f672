import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared/components/common';

const AgentManagementPage = lazy(() => import('../pages/AgentManagementPage'));
const AgentSystemPage = lazy(() => import('../pages/AgentSystemPage'));
const AgentSystemDeletePage = lazy(() => import('../pages/AgentSystemDeletePage'));
const AgentRankPage = lazy(() => import('../pages/AgentRankPage'));
const AgentStrategyPage = lazy(() => import('../pages/AgentStrategyPage'));
const AgentTypePage = lazy(() => import('../pages/AgentTypePage'));
const AgentTypeDeletePage = lazy(() => import('../pages/AgentTypeDeletePage'));

/**
 * Routes cho module Admin Agent
 */
export const adminAgentRoutes: RouteObject[] = [
  {
    path: '/admin/agent',
    element: (
      <AdminLayout title="admin:agent.management.title">
        <Suspense fallback={<Loading />}>
          <AgentManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/system',
    element: (
      <AdminLayout title="admin:agent.system.title">
        <Suspense fallback={<Loading />}>
          <AgentSystemPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/system/trash',
    element: (
      <AdminLayout title="admin:agent.trash.title">
        <Suspense fallback={<Loading />}>
          <AgentSystemDeletePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/ranks',
    element: (
      <AdminLayout title="admin:agent.rank.title">
        <Suspense fallback={<Loading />}>
          <AgentRankPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/strategy',
    element: (
      <AdminLayout title="admin:agent.strategy.title">
        <Suspense fallback={<Loading />}>
          <AgentStrategyPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/types',
    element: (
      <AdminLayout title="admin:agent.type.title">
        <Suspense fallback={<Loading />}>
          <AgentTypePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/type/trash',
    element: (
      <AdminLayout title="admin:agent.trash.title">
        <Suspense fallback={<Loading />}>
          <AgentTypeDeletePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  // TODO: Thêm các route khác
  // {
  //   path: '/admin/agent/users',
  //   element: (
  //     <AdminLayout title="Quản lý User Agent">
  //       <Suspense fallback={<Loading />}>
  //         <AgentUserPage />
  //       </Suspense>
  //     </AdminLayout>
  //   ),
  // },
];

export default adminAgentRoutes;