import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, ResponsiveImage } from '@/shared/components/common';
import AdminForgotPasswordForm from '../components/AdminForgotPasswordForm';
// Import logo
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Admin forgot password page
 */
const AdminForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleBackToLogin = () => {
    navigate('/admin/auth');
  };

  return (
    <Card variant="elevated" className="w-full max-w-md p-8">
      <div className="flex flex-col items-center mb-8">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-10">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-8 object-contain max-w-[120px]"
          />
        </div>
      </div>

      <div className="mb-6">
        <div className="text-center mb-6">
          <Typography variant="h5" className="font-bold mb-2">
            {t('auth:admin.forgotPassword')}
          </Typography>
          <Typography variant="body2" color="muted">
            {t('auth:admin.forgotPasswordDescription')}
          </Typography>
        </div>

        <div className="space-y-6">
          <AdminForgotPasswordForm onBackToLogin={handleBackToLogin} />
        </div>
      </div>
    </Card>
  );
};

export default AdminForgotPasswordPage;
