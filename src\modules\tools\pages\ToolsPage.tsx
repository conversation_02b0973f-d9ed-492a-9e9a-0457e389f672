import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Card, Typography, Button, Pagination } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ToolGrid, ToolForm } from '../components';
import {
  useUserTools,
  useEditToolVersion,
  useToggleToolActive,
  useCloneAllPublicTools,
  useUserToolDetail,
  useUpdateFromAdmin,
  useRollbackToAdminVersion,
} from '../hooks/useTool';

import { ToolStatus, ToolSortBy } from '../types/common.types';
import { ToolListItem, ToolDetail } from '../types/tool.types';
import { EditUserToolVersionParams } from '../types/user-tool.types';

/**
 * Trang hiển thị danh sách tools của người dùng
 */
const ToolsPage: React.FC = () => {
  const { t } = useTranslation(['tools', 'common']);
  const navigate = useNavigate();

  // State cho tìm kiếm và lọc
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ToolStatus | 'all'>('all');
  const [filterHasUpdate, setFilterHasUpdate] = useState<boolean | undefined>(undefined);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State cho xem chi tiết tool
  const [toolToView, setToolToView] = useState<ToolListItem | null>(null);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      status: filterStatus !== 'all' ? filterStatus : undefined,
      hasUpdate: filterHasUpdate,
      sortBy: ToolSortBy.CREATED_AT,
      sortDirection: 'DESC' as const,
    }),
    [currentPage, itemsPerPage, searchTerm, filterStatus, filterHasUpdate]
  );

  // Hooks để gọi API
  const { data: toolsData, isLoading } = useUserTools({
    ...queryParams,
    search: queryParams.search ?? '',
    status: queryParams.status ?? ToolStatus.APPROVED,
    hasUpdate: queryParams.hasUpdate ?? false,
  });

  const { data: toolDetail, isLoading: isLoadingDetail } = useUserToolDetail(toolToView?.id);

  const cloneAllPublicToolsMutation = useCloneAllPublicTools();
  const updateFromAdminMutation = useUpdateFromAdmin();
  const rollbackToAdminMutation = useRollbackToAdminVersion();

  const editVersionMutation = useEditToolVersion();
  const toggleActiveMutation = useToggleToolActive();

  const cloneAllPublicTools = cloneAllPublicToolsMutation.mutateAsync;
  const isCloning = cloneAllPublicToolsMutation.isPending || false;
  const updateFromAdmin = updateFromAdminMutation.mutateAsync;
  const rollbackToAdmin = rollbackToAdminMutation.mutateAsync;

  const editVersion = editVersionMutation.mutateAsync;
  const toggleActive = toggleActiveMutation.mutateAsync;

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = useCallback((status: string) => {
    setFilterStatus(status as ToolStatus | 'all');
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Các hàm xử lý lọc theo trạng thái cụ thể
  const handleFilterAll = useCallback(() => handleFilterStatus('all'), [handleFilterStatus]);
  const handleFilterDraft = useCallback(
    () => handleFilterStatus(ToolStatus.DRAFT),
    [handleFilterStatus]
  );
  const handleFilterApproved = useCallback(
    () => handleFilterStatus(ToolStatus.APPROVED),
    [handleFilterStatus]
  );
  const handleFilterDeprecated = useCallback(
    () => handleFilterStatus(ToolStatus.DEPRECATED),
    [handleFilterStatus]
  );

  // Xử lý lọc theo có cập nhật hay không
  const handleFilterHasUpdate = useCallback((hasUpdate: boolean | undefined) => {
    setFilterHasUpdate(hasUpdate);
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Các hàm xử lý lọc theo cập nhật cụ thể
  const handleFilterHasUpdateTrue = useCallback(
    () => handleFilterHasUpdate(true),
    [handleFilterHasUpdate]
  );
  const handleFilterHasUpdateFalse = useCallback(
    () => handleFilterHasUpdate(false),
    [handleFilterHasUpdate]
  );

  // Xử lý xem chi tiết tool
  const handleViewTool = useCallback(
    (tool: ToolListItem) => {
      setToolToView(tool);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý đóng form chi tiết
  const handleCloseDetailForm = useCallback(() => {
    hideDetailForm();
    setToolToView(null);
  }, [hideDetailForm]);

  // Xử lý sao chép tất cả tool công khai
  const handleCloneAllPublicTools = useCallback(async () => {
    try {
      await cloneAllPublicTools({});
    } catch (error) {
      console.error('Error cloning all public tools:', error);
    }
  }, [cloneAllPublicTools]);

  // Xử lý cập nhật từ admin
  const handleUpdateFromAdmin = useCallback(
    async (toolId: string, adminVersionId: string) => {
      try {
        await updateFromAdmin({
          userToolId: toolId,
          adminVersionId,
        });
      } catch (error) {
        console.error('Error updating from admin:', error);
      }
    },
    [updateFromAdmin]
  );
  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Xử lý thay đổi số mục trên trang
  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
  }, []);
  // Xử lý khôi phục về phiên bản admin
  const handleRollbackToAdmin = useCallback(
    async (toolId: string, adminVersionId: string) => {
      try {
        await rollbackToAdmin({
          userToolId: toolId,
          adminVersionId,
        });
      } catch (error) {
        console.error('Error rolling back to admin version:', error);
      }
    },
    [rollbackToAdmin]
  );

  // Xử lý xem danh sách phiên bản
  const handleViewVersions = useCallback(
    (toolId: string) => {
      console.log('ToolsPage: handleViewVersions called for toolId:', toolId);
      navigate(`/tools/${toolId}/versions`);
    },
    [navigate]
  );

  // Xử lý submit version từ ToolForm
  const handleSubmitVersion = useCallback(
    async (versionId: string, values: EditUserToolVersionParams) => {
      if (!toolToView) return;

      try {
        await editVersion({
          toolId: toolToView.id,
          versionId: versionId,
          params: values,
        });
        NotificationUtil.success({
          message: t('tools:updateVersionSuccess', 'Phiên bản đã được cập nhật thành công!'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error editing version:', error);
        NotificationUtil.error({
          message: t(
            'tools:updateVersionError',
            'Có lỗi xảy ra khi cập nhật phiên bản. Vui lòng thử lại.'
          ),
          duration: 5000,
        });
      }
    },
    [editVersion, toolToView, t]
  );

  // Xử lý toggle active tool
  const handleToggleActive = useCallback(
    async (toolId: string) => {
      console.log('ToolsPage: handleToggleActive called for toolId:', toolId);
      try {
        await toggleActive(toolId);
        console.log('ToolsPage: toggleActive API call successful');
      } catch (error) {
        console.error('Error toggling tool active:', error);
      }
    },
    [toggleActive]
  );

  // Memoize items array để tránh re-render không cần thiết
  const menuItems = useMemo(
    () => [
      {
        id: 'all',
        label: t('common.all', 'Tất cả'),
        icon: 'list',
        onClick: handleFilterAll,
      },
      {
        id: 'draft',
        label: t('tools:status.draft', 'Bản nháp'),
        icon: 'file',
        onClick: handleFilterDraft,
      },
      {
        id: 'approved',
        label: t('tools:status.approved', 'Đã duyệt'),
        icon: 'check-circle',
        onClick: handleFilterApproved,
      },
      {
        id: 'deprecated',
        label: t('tools:status.deprecated', 'Không dùng'),
        icon: 'archive',
        onClick: handleFilterDeprecated,
      },
      {
        id: 'has-update',
        label: t('tools:hasUpdate', 'Có cập nhật'),
        icon: 'refresh-cw',
        onClick: handleFilterHasUpdateTrue,
      },
      {
        id: 'no-update',
        label: t('tools:noUpdate', 'Không có cập nhật'),
        icon: 'check',
        onClick: handleFilterHasUpdateFalse,
      },
    ],
    [
      t,
      handleFilterAll,
      handleFilterDraft,
      handleFilterApproved,
      handleFilterDeprecated,
      handleFilterHasUpdateTrue,
      handleFilterHasUpdateFalse,
    ]
  );

  return (
    <div className="space-y-4">
      {/* Menu bar */}
      <MenuIconBar onSearch={handleSearch} items={menuItems} />

      {/* SlideInForm cho form xem chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        <ToolForm
          tool={toolDetail as ToolDetail}
          isLoading={isLoadingDetail}
          onClose={handleCloseDetailForm}
          onUpdateFromAdmin={handleUpdateFromAdmin}
          onRollbackToAdmin={handleRollbackToAdmin}
          onSubmitVersion={handleSubmitVersion}
        />
      </SlideInForm>

      {/* Hiển thị danh sách tool */}
      {toolsData?.items && toolsData.items.length > 0 ? (
        <>
          <ToolGrid
            tools={toolsData.items}
            onViewTool={handleViewTool}
            onToggleActive={handleToggleActive}
            onViewVersions={handleViewVersions}
          />
          {/* Phân trang */}
          {toolsData.meta && toolsData.meta.totalItems > 0 && (
            <div className="mt-6 flex justify-end">
              <Pagination
                variant="simple"
                currentPage={currentPage}
                totalItems={toolsData.meta.totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showFirstLastButtons={false}
                showItemsPerPageSelector={true}
                showPageInfo={false}
                size="md"
              />
            </div>
          )}
        </>
      ) : (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center">
            <Typography variant="h6" className="text-gray-500 dark:text-gray-400">
              {isLoading
                ? t('common:loading', 'Đang tải...')
                : t('tools:noTools', 'Không tìm thấy công cụ nào')}
            </Typography>
            {!isLoading && (
              <Button
                variant="primary"
                className="mt-4"
                onClick={handleCloneAllPublicTools}
                disabled={isCloning}
              >
                {isCloning
                  ? t('tools:cloning', 'Đang sao chép...')
                  : t('tools:cloneAllPublic', 'Sao chép tất cả công cụ công khai')}
              </Button>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default ToolsPage;
