import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { Typography, Button, Alert, OTPInput, IconCard } from '@/shared/components/common';
import { useVerifyForgotPassword, useResendOtp } from '../../../auth/hooks/useAuthQuery';
import CountdownTimer from '../../../auth/components/CountdownTimer';
import { VerifyForgotPasswordRequest, ResendOtpRequest } from '../../../auth/types/auth.types';

/**
 * Admin verify forgot password page
 */
const AdminVerifyForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [otp, setOtp] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);

  const { mutate: verifyForgotPassword, isPending: isVerifying } = useVerifyForgotPassword();
  const { mutate: resendOtp, isPending: isResending } = useResendOtp();

  // Lấy thông tin từ localStorage thay vì Redux
  const verifyToken = localStorage.getItem('verifyToken');
  const expiresInStr = localStorage.getItem('expiresIn');
  const verifyExpiresAt = expiresInStr ? Date.now() + parseInt(expiresInStr) * 1000 : undefined;
  const email = localStorage.getItem('resetEmail');

  // Kiểm tra xem có thông tin xác thực không
  useEffect(() => {
    if (!verifyToken || !email) {
      navigate('/admin/auth/forgot-password');
    }
  }, [verifyToken, email, navigate]);

  // Xử lý xác thực OTP
  const handleVerifyOtp = () => {
    if (otp.length !== 6) {
      setErrorMessage(t('auth:invalidOtp'));
      return;
    }

    setErrorMessage(null);

    if (verifyToken) {
      verifyForgotPassword(
        {
          otpToken: verifyToken,
          otp: otp,
          role: 'admin',
        } as unknown as VerifyForgotPasswordRequest,
        {
          onSuccess: response => {
            // Chuyển hướng đến trang đặt lại mật khẩu
            // Giả định response.result có cấu trúc phù hợp
            const result = response.result as unknown as { resetToken?: string };

            if (result && result.resetToken) {
              localStorage.setItem('resetToken', result.resetToken);
              navigate('/admin/auth/reset-password');
            } else {
              // Nếu không có resetToken, quay lại trang đăng nhập
              // Xóa thông tin xác thực từ localStorage
              localStorage.removeItem('verifyToken');
              localStorage.removeItem('expiresIn');
              localStorage.removeItem('resetEmail');
              navigate('/admin/auth');
            }
          },
          onError: (error: unknown) => {
            console.error('Verify OTP error:', error);

            let errorMsg = t('auth:verifyOtpError');

            if (error && typeof error === 'object' && 'response' in error && error.response) {
              const axiosError = error as { response: { data?: { message?: string } } };
              if (axiosError.response.data?.message) {
                errorMsg = axiosError.response.data.message;
              }
            }

            setErrorMessage(errorMsg);
          },
        }
      );
    }
  };

  // Xử lý gửi lại OTP
  const handleResendOtp = () => {
    if (email && verifyToken) {
      resendOtp(
        {
          otpToken: verifyToken,
          role: 'admin',
        } as unknown as ResendOtpRequest,
        {
          onSuccess: response => {
            if (response.result) {
              // Cập nhật thông tin xác thực trong Redux store
              // Trong thực tế, cần kiểm tra và lưu thông tin mới từ response
              // Nhưng ở đây chỉ cần reset trạng thái

              // Cập nhật state
              setIsOtpExpired(false);
              setErrorMessage(null);
            }
          },
          onError: (error: unknown) => {
            console.error('Resend OTP error:', error);

            let errorMsg = t('auth:resendOtpError');

            if (error && typeof error === 'object' && 'response' in error && error.response) {
              const axiosError = error as { response: { data?: { message?: string } } };
              if (axiosError.response.data?.message) {
                errorMsg = axiosError.response.data.message;
              }
            }

            setErrorMessage(errorMsg);
          },
        }
      );
    }
  };

  // Xử lý quay lại trang đăng nhập
  const handleBackToLogin = () => {
    // Xóa thông tin xác thực từ localStorage
    localStorage.removeItem('verifyToken');
    localStorage.removeItem('expiresIn');
    localStorage.removeItem('resetEmail');
    navigate('/admin/auth');
  };

  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-6">
        <Typography variant="h5" className="font-bold mb-2">
          {t('auth:verifyOtp')}
        </Typography>
        <Typography variant="body2" color="muted">
          {t('auth:resetPasswordDescription')}
        </Typography>

        {email && (
          <Typography variant="body2" className="mt-2 font-medium">
            {email}
          </Typography>
        )}

        {/* Hiển thị đồng hồ đếm ngược */}
        {verifyExpiresAt ? (
          <CountdownTimer
            expiresAt={verifyExpiresAt}
            onExpire={() => setIsOtpExpired(true)}
            className="mb-4 mt-2"
          />
        ) : null}
      </div>

      <div className="space-y-6">
        <div>
          <OTPInput
            length={6}
            onChange={setOtp}
            onEnterPress={handleVerifyOtp}
            autoFocus
            className="mb-4"
            disabled={isOtpExpired}
          />
        </div>

        {isOtpExpired ? (
          <Alert type="warning" message={t('auth:otpExpiredMessage')} className="mb-4" />
        ) : null}

        {errorMessage && <Alert type="error" message={errorMessage} className="mb-4" />}

        <Button
          type="button"
          variant="primary"
          fullWidth
          onClick={handleVerifyOtp}
          isLoading={isVerifying}
          disabled={otp.length !== 6 || isOtpExpired}
        >
          {t('auth:verify')}
        </Button>

        <div className="flex justify-between items-center mt-4">
          <Typography variant="body2" color="muted">
            {t('auth.rememberPassword')}{' '}
            <Link to="/auth">
              <IconCard
                icon="arrow-left"
                onClick={handleBackToLogin}
                title={t('auth.backToLogin', 'Quay lại đăng nhập')}
                className="cursor-pointer"
              />
            </Link>
          </Typography>

          <Button
            type="button"
            variant="outline"
            onClick={handleResendOtp}
            isLoading={isResending}
            disabled={!isOtpExpired}
            className="text-primary hover:text-primary/80"
          >
            {t('auth:resendCode')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminVerifyForgotPasswordPage;
