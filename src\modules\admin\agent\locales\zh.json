{"agent": {"management": {"title": "代理管理", "description": "综合代理系统管理"}, "notification": {"createSuccess": "{{entityName}}已成功创建", "updateSuccess": "{{entityName}}已成功更新", "deleteSuccess": "{{entityName}}已成功删除", "restoreSuccess": "{{entityName}}已成功恢复", "createError": "创建{{entityName}}时发生错误", "updateError": "更新{{entityName}}时发生错误", "deleteError": "删除{{entityName}}时发生错误", "restoreError": "恢复{{entityName}}时发生错误", "loadError": "无法加载{{entityName}}列表", "uploadSuccess": "上传成功", "uploadSuccessWithName": "{{fileName}}上传成功", "uploadError": "上传时发生错误", "validationError": "数据无效", "permissionError": "您没有权限执行此操作", "networkError": "网络连接错误。请重试", "processing": "正在{{action}}..."}, "rank": {"title": "代理等级管理", "description": "代理排名管理", "pageTitle": "代理等级管理", "addRank": "添加新等级", "editRank": "编辑代理等级", "searchPlaceholder": "搜索等级...", "noSearchResults": "未找到符合搜索条件的等级。", "createFirst": "创建第一个代理等级", "sortBy": "排序方式", "createSuccess": "成功", "createSuccessMessage": "代理等级已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理等级已成功更新", "active": "活跃", "inactive": "非活跃", "editAction": "编辑", "deleteAction": "删除", "confirmDelete": "确认删除等级", "deleteMessage": "您确定要删除此等级吗？此操作无法撤销。", "deleteSuccess": "等级删除成功", "deleteError": "删除等级时发生错误", "list": {"title": "等级列表", "noRanks": "没有等级", "noRanksDescription": "系统中目前没有等级。", "loadError": "无法加载等级列表。请重试。", "loading": "正在加载等级列表...", "refreshing": "正在刷新数据..."}, "form": {"basicInfo": "基本信息", "name": "等级名称", "namePlaceholder": "输入等级名称", "description": "描述", "descriptionPlaceholder": "输入等级描述", "expRange": "经验范围", "minExp": "最低经验", "maxExp": "最高经验", "badge": "徽章", "badgeUpload": "上传徽章", "badgeHelp": "支持格式：JPG、PNG（仅限单张图片）", "currentBadge": "当前徽章", "currentBadgeNote": "上传新图片以替换", "active": "激活", "create": "创建等级", "update": "更新", "creating": "正在创建等级...", "createSuccess": "等级创建成功", "createError": "创建等级时发生错误", "updateError": "更新等级时发生错误"}, "validation": {"nameRequired": "等级名称是必需的", "descriptionRequired": "描述是必需的", "minExpInvalid": "最低经验必须 >= 0", "minExpInteger": "最低经验必须是整数", "maxExpInvalid": "最高经验必须 > 0", "maxExpInteger": "最高经验必须是整数", "expRangeInvalid": "最高经验必须大于最低经验", "expRangeOverlap": "经验范围与其他等级的经验范围重叠"}, "edit": {"notFound": "未找到等级"}, "sort": {"name": "名称", "minExp": "最低经验", "maxExp": "最高经验", "createdAt": "创建日期"}}, "system": {"title": "系统代理管理", "description": "系统代理管理", "pageTitle": "系统代理管理", "addAgent": "添加新代理", "editAgent": "编辑代理系统", "searchPlaceholder": "搜索代理...", "noSearchResults": "未找到符合搜索条件的代理。", "createFirst": "创建第一个系统代理", "viewTrash": "查看回收站", "backToMain": "返回主列表", "createSuccess": "成功", "createSuccessMessage": "代理系统已成功创建", "updateSuccess": "成功", "cancel": "取消", "updateAgent": "更新代理", "updateSuccessMessage": "代理系统已成功更新"}, "user": {"title": "管理员 - 用户代理", "description": "用户代理管理"}, "type": {"title": "代理类型管理", "description": "代理类型管理", "pageTitle": "代理类型管理", "addType": "添加新代理类型", "editType": "编辑代理类型", "searchPlaceholder": "搜索代理类型...", "createFirst": "创建第一个代理类型", "createSuccess": "成功", "createSuccessMessage": "代理类型已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理类型已成功更新", "cancel": "取消", "updateType": "更新代理类型", "viewTrash": "查看回收站", "backToMain": "返回主列表", "noSearchResults": "未找到符合搜索条件的代理类型。", "deleteConfirmTitle": "确认删除代理类型", "deleteConfirmMessage": "您确定要删除此代理类型吗？此操作将把代理类型移至回收站，可以恢复。", "deleteSuccess": "删除成功", "deleteSuccessMessage": "代理类型已成功删除", "restoreSuccess": "恢复成功", "restoreSuccessMessage": "代理类型已成功恢复", "selectTypeToDelete": "选择要删除的代理类型", "selectTypeToDeleteDescription": "选择您要删除的代理类型。代理类型将被移至回收站，可以恢复。", "deleteWithMigration": "删除并迁移", "deleteWithMigrationDescription": "删除代理类型并将此类型的所有代理迁移到新选择的类型。", "newTypeAgent": "新代理类型", "selectNewType": "选择新代理类型", "selectNewTypeDescription": "选择要将当前代理迁移到的新代理类型。", "noAvailableTypes": "没有其他代理类型可用于迁移。您只能删除而不迁移。", "deleteOnly": "仅删除", "deleteError": "删除代理类型时发生错误", "restoreError": "恢复代理类型时发生错误"}, "trash": {"title": "回收站 - 代理类型", "noAgents": "回收站中没有代理类型", "noAgentsDescription": "回收站为空。已删除的代理类型将显示在这里。", "restoreAgent": "恢复代理类型", "permanentDelete": "永久删除"}, "list": {"title": "代理类型列表", "noTypes": "没有代理类型", "noTypesDescription": "系统中目前没有代理类型。", "loadError": "无法加载代理类型列表。请重试。", "loading": "正在加载代理类型列表...", "refreshing": "正在刷新数据..."}, "card": {"edit": "编辑", "delete": "删除", "confirmDelete": "确认删除代理类型", "deleteMessage": "您确定要删除此代理类型吗？此操作无法撤销。", "deleteSuccess": "代理类型删除成功", "deleteError": "删除代理类型时发生错误", "updateSuccess": "代理类型更新成功", "updateError": "更新代理类型时发生错误"}, "form": {"basicInfo": "基本信息", "name": "代理类型名称", "nameCode": "标识符代码", "nameCodePlaceholder": "输入标识符代码", "instruction": "指令", "instructionPlaceholder": "输入代理类型指令", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "currentAvatar": "当前头像", "currentAvatarNote": "上传新图片以替换", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "provider": "提供商类型", "resources": "资源", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "namePlaceholder": "输入代理类型名称", "description": "描述", "descriptionPlaceholder": "输入代理类型描述", "defaultConfig": "默认配置", "enableAgentProfileCustomization": "启用代理配置文件自定义", "enableOutputToMessenger": "启用输出到Messenger", "enableOutputToWebsiteLiveChat": "启用输出到网站实时聊天", "enableTaskConversionTracking": "启用任务转换跟踪", "enableResourceUsage": "启用资源使用", "enableDynamicStrategyExecution": "启用动态策略执行", "enableMultiAgentCollaboration": "启用多代理协作", "enableOutputToZaloOA": "启用输出到Zalo OA", "status": "状态", "selectStatus": "选择状态", "draft": "草稿", "approved": "已批准", "agentSystems": "代理系统", "selectAgentSystems": "选择代理系统", "agentSystemsConfig": {"title": "代理系统", "noSystemsSelected": "未选择任何代理系统", "systemCount": "已选择 {{count}} 个代理系统", "addSystem": "添加代理系统", "selectedSystems": "已选择的代理系统", "removeSystem": "移除代理系统", "removeSystemSuccess": "代理系统移除成功", "removeSystemError": "移除代理系统时出错", "confirmDeleteSystem": "您确定要从此代理类型中移除代理系统 \"{{systemName}\" 吗？", "deleteSystemWarning": "此操作无法撤销。", "createdAt": "创建时间"}, "agentSystemSlideIn": {"title": "选择代理系统", "close": "关闭", "cancel": "取消", "save": "保存", "system": "系统", "status": "状态", "active": "活跃", "inactive": "非活跃", "createdAt": "创建时间", "filterBy": "筛选", "all": "全部", "updateSystemsSuccess": "代理系统更新成功", "updateSystemsError": "更新代理系统时出错", "addSystemsToListSuccess": "代理系统添加到列表成功", "cannotSaveInThisMode": "无法在此模式下保存"}, "create": "创建代理类型", "creating": "正在创建代理类型...", "createSuccess": "代理类型创建成功", "createError": "创建代理类型时发生错误", "loadAgentSystemsError": "无法加载代理系统列表"}, "validation": {"nameRequired": "代理类型名称是必需的", "descriptionRequired": "描述是必需的", "statusRequired": "状态是必需的", "agentSystemsRequired": "至少需要一个代理系统"}, "common": {"confirmDelete": "确认删除", "cancel": "取消", "delete": "删除", "error": "错误", "locale": "zh-CN", "success": "成功", "loading": "加载中...", "save": "保存", "close": "关闭", "edit": "编辑", "view": "查看", "back": "返回", "next": "下一步", "previous": "上一步", "search": "搜索", "filter": "筛选", "all": "全部", "active": "活跃", "inactive": "非活跃", "draft": "草稿", "approved": "已批准", "create": "创建", "update": "更新", "refresh": "刷新", "restore": "恢复"}, "deleteConfirmTitle": "确认删除代理类型", "deleteConfirmMessage": "您确定要删除此代理类型吗？此操作将把代理类型移至回收站，可以恢复。", "deleteSuccess": "删除成功", "deleteSuccessMessage": "代理类型已成功删除", "deleteError": "删除代理类型时发生错误", "selectTypeToDelete": "选择要删除的代理类型", "deleteWithMigration": "删除并迁移", "deleteWithMigrationDescription": "删除代理类型并将此类型的所有代理迁移到新选择的类型。", "newTypeAgent": "新代理类型", "selectNewType": "选择新代理类型", "selectNewTypeDescription": "选择新代理类型以迁移当前代理。", "noAvailableTypes": "没有其他代理类型可用于迁移。您只能删除而不迁移。", "deleteOnly": "仅删除"}, "strategy": {"title": "代理策略管理", "description": "代理策略管理", "pageTitle": "代理策略管理", "addStrategy": "添加新策略", "editStrategy": "编辑代理策略", "searchPlaceholder": "搜索策略...", "noSearchResults": "未找到符合搜索条件的策略。", "createFirst": "创建第一个代理策略", "createSuccess": "成功", "createSuccessMessage": "代理策略已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理策略已成功更新", "cancel": "取消", "updateStrategy": "更新策略", "list": {"title": "策略列表", "noStrategies": "没有策略", "noStrategiesDescription": "系统中目前没有策略。", "loadError": "无法加载策略列表。请重试。", "loading": "正在加载策略列表...", "refreshing": "正在刷新数据..."}, "card": {"edit": "编辑", "delete": "删除", "confirmDelete": "确认删除策略", "deleteMessage": "您确定要删除此策略吗？此操作无法撤销。", "deleteSuccess": "策略删除成功", "deleteError": "删除策略时发生错误", "updateSuccess": "策略更新成功", "updateError": "更新策略时发生错误"}, "form": {"basicInfo": "基本信息", "name": "策略名称", "namePlaceholder": "输入策略名称", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "modelConfig": "模型配置", "temperature": "温度", "maxTokens": "最大令牌数", "instruction": "指令", "instructionPlaceholder": "输入策略指令", "content": "步骤内容", "contentStep": "步骤 {step}", "contentPlaceholder": "输入步骤 {step} 的内容", "addStep": "添加步骤", "removeStep": "删除步骤", "exampleDefault": "默认示例", "exampleStep": "示例 {step}", "examplePlaceholder": "输入步骤 {step} 的示例", "addExample": "添加示例", "removeExample": "删除示例", "systemModel": "系统模型", "provider": "提供商类型", "selectProvider": "选择提供商", "selectProviderFirst": "请先选择提供商", "model": "模型", "selectSystemModel": "选择系统模型", "selectModel": "选择模型", "create": "创建策略", "creating": "正在创建策略...", "createSuccess": "策略创建成功", "createError": "创建策略时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误", "loadSystemModelsError": "无法加载系统模型列表"}, "validation": {"nameRequired": "策略名称是必需的", "instructionRequired": "指令是必需的", "systemModelRequired": "系统模型是必需的", "providerRequired": "请选择提供商", "contentRequired": "步骤内容是必需的", "contentStepRequired": "步骤 {step} 的内容是必需的", "exampleRequired": "默认示例是必需的", "exampleStepRequired": "步骤 {step} 的示例是必需的"}}, "list": {"title": "代理列表", "noAgents": "没有代理", "noAgentsDescription": "系统中目前没有代理。", "loadError": "无法加载代理列表。请重试。", "loading": "正在加载代理列表...", "refreshing": "正在刷新数据..."}, "card": {"supervisor": "监督员", "active": "活跃", "inactive": "非活跃", "activate": "激活", "deactivate": "停用", "edit": "编辑", "delete": "删除", "restore": "恢复", "confirmDelete": "确认删除代理", "deleteMessage": "您确定要删除此代理吗？此操作无法撤销。", "deleteSuccess": "代理删除成功", "deleteError": "删除代理时发生错误", "updateSuccess": "代理更新成功", "updateError": "更新代理时发生错误", "setSupervisor": "设为监督员", "removeSupervisor": "移除监督员", "setSupervisorSuccess": "成功设为监督员", "removeSupervisorSuccess": "成功移除监督员权限", "supervisorError": "更改监督员权限时发生错误", "restoreSuccess": "代理恢复成功", "restoreError": "恢复代理时发生错误"}, "trash": {"noAgents": "回收站中没有代理", "noAgentsDescription": "回收站为空。已删除的代理将出现在这里。"}, "edit": {"notFound": "未找到代理"}, "pagination": {"itemsPerPage": "每页项目数", "showingItems": "显示 {from} - {to} 共 {total} 项", "page": "页", "of": "共", "previous": "上一页", "next": "下一页"}, "form": {"basicInfo": "基本信息", "name": "代理名称", "namePlaceholder": "输入代理名称", "nameCode": "标识符代码", "nameCodePlaceholder": "输入标识符代码", "instruction": "指令", "instructionPlaceholder": "输入代理指令", "description": "描述", "descriptionPlaceholder": "输入代理描述", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "provider": "提供商类型", "resources": "资源", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "isSupervisor": "是监督员", "create": "创建代理", "creating": "正在创建代理...", "createSuccess": "代理创建成功", "createError": "创建代理时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误"}, "validation": {"nameRequired": "代理名称是必需的", "nameCodeRequired": "标识符代码是必需的", "nameCodeFormat": "标识符代码只能包含小写字母、数字、下划线和连字符", "instructionRequired": "指令是必需的", "descriptionRequired": "描述是必需的", "modelRequired": "模型是必需的", "modelIdInvalid": "模型ID必须是有效的UUID"}}