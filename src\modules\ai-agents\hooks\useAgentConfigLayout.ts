/**
 * React Hook cho Agent Configuration Layout
 * S<PERSON> dụng XState để quản lý layout state
 */

import { useEffect, useCallback, useMemo, useState } from 'react';
import { useMachine } from '@xstate/react';
import { layoutMachine, LayoutContext } from '../machines/layoutMachine';
import { TypeAgentConfig } from '../types';

// Event types for the layout machine
type LayoutEvent =
  | { type: 'CONFIG_CHANGED'; config: TypeAgentConfig }
  | { type: 'RESIZE'; breakpoint: 'mobile' | 'tablet' | 'desktop' }
  | { type: 'FORCE_LAYOUT'; mode: LayoutContext['layoutMode'] };

/**
 * Breakpoint definitions
 * Điều chỉnh để layout 2 cột hiển thị sớm hơn
 */
const BREAKPOINTS = {
  mobile: 640,  // sm breakpoint
  tablet: 768,  // md breakpoint
} as const;

/**
 * Hook để detect responsive breakpoint
 */
const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < BREAKPOINTS.mobile) {
        setBreakpoint('mobile');
      } else if (width < BREAKPOINTS.tablet) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    // Initial check
    updateBreakpoint();

    // Listen for resize
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

/**
 * Hook chính cho Agent Configuration Layout
 */
export const useAgentConfigLayout = (typeAgentConfig: TypeAgentConfig) => {
  const breakpoint = useBreakpoint();

  const [state, send] = useMachine(layoutMachine.provide({
    guards: {},
    actions: {}
  }));

  // Memoize send function để tránh re-render không cần thiết
  const stableSend = useCallback((event: LayoutEvent) => {
    send(event);
  }, [send]);

  // Update config khi typeAgentConfig thay đổi - sử dụng JSON.stringify để so sánh deep
  const typeAgentConfigString = useMemo(() =>
    JSON.stringify(typeAgentConfig), [typeAgentConfig]
  );

  useEffect(() => {
    console.log('useAgentConfigLayout - CONFIG_CHANGED:', { typeAgentConfig });
    stableSend({ type: 'CONFIG_CHANGED', config: typeAgentConfig });
  }, [typeAgentConfigString, stableSend, typeAgentConfig]);

  // Update breakpoint khi responsive thay đổi
  useEffect(() => {
    stableSend({ type: 'RESIZE', breakpoint });
  }, [breakpoint, stableSend]);

  // Responsive column ratio dựa trên breakpoint
  const responsiveColumnRatio = useMemo(() => {
    const [leftRatio, rightRatio] = state.context.columnRatio;

    switch (breakpoint) {
      case 'mobile':
        // Mobile: luôn single column
        return [100, 0];
      case 'tablet':
        // Tablet: cân bằng hơn
        if (state.context.layoutMode === 'RIGHT_HEAVY') {
          return [45, 55];
        }
        return [50, 50];
      case 'desktop':
      default:
        return [leftRatio, rightRatio];
    }
  }, [state.context.columnRatio, state.context.layoutMode, breakpoint]);

  // CSS Grid template columns
  const gridTemplateColumns = useMemo(() => {
    const [leftRatio, rightRatio] = responsiveColumnRatio;

    switch (state.context.layoutMode) {
      case 'EMPTY':
        return '1fr';
      case 'LEFT_ONLY':
        return '1fr';
      case 'RIGHT_ONLY':
        return '1fr';
      case 'RIGHT_HEAVY':
      case 'BALANCED':
        if (breakpoint === 'mobile') {
          return '1fr'; // Single column on mobile
        }
        return `${leftRatio}fr ${rightRatio}fr`;
      default:
        return '1fr 1fr';
    }
  }, [state.context.layoutMode, responsiveColumnRatio, breakpoint]);

  // Check if component should be visible
  const isComponentVisible = useCallback((componentName: string): boolean => {
    const allComponents = [
      ...state.context.availableComponents.left,
      ...state.context.availableComponents.right
    ];
    return allComponents.some(comp => comp.name === componentName);
  }, [state.context.availableComponents]);

  // Get component column
  const getComponentColumn = useCallback((componentName: string): 'left' | 'right' | null => {
    if (state.context.availableComponents.left.some(comp => comp.name === componentName)) {
      return 'left';
    }
    if (state.context.availableComponents.right.some(comp => comp.name === componentName)) {
      return 'right';
    }
    return null;
  }, [state.context.availableComponents]);

  // Force layout mode (for testing/debugging)
  const forceLayoutMode = useCallback((mode: LayoutContext['layoutMode']) => {
    send({ type: 'FORCE_LAYOUT', mode });
  }, [send]);

  return {
    // Layout state
    layoutMode: state.context.layoutMode,
    columnRatio: responsiveColumnRatio,
    availableComponents: state.context.availableComponents,
    breakpoint,

    // CSS properties
    gridTemplateColumns,

    // Helper functions
    isComponentVisible,
    getComponentColumn,
    forceLayoutMode,

    // State machine info
    isAnalyzing: state.matches('analyzing'),
    currentState: state.value,

    // Debug info
    debugInfo: {
      originalColumnRatio: state.context.columnRatio,
      responsiveColumnRatio,
      totalLeftComponents: state.context.availableComponents.left.length,
      totalRightComponents: state.context.availableComponents.right.length,
    }
  };
};


