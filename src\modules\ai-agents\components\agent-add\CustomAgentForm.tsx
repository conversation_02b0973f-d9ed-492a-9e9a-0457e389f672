import React, { useState } from 'react';
import { Card, Input, Textarea, Button } from '@/shared/components/common';
import GroupToolTable, { GroupTool } from './GroupToolTable';
import ConfigFieldsGrid from '../common/ConfigFieldsGrid';
import { FULL_TYPE_AGENT_CONFIG_FIELDS } from '../../utils';
import { CustomAgentFormData } from './CustomTypeAgentCard';

interface CustomAgentFormProps {
  onSave: (data: CustomAgentFormData) => void;
  onCancel: () => void;
}

/**
 * Component form cấu hình Custom Agent
 */
const CustomAgentForm: React.FC<CustomAgentFormProps> = ({ onSave, onCancel }) => {
  // Dữ liệu mẫu cho các group tool
  const sampleGroupTools: GroupTool[] = [];

  // State cho form
  const [formData, setFormData] = useState<CustomAgentFormData>({
    name: "Custom Agent",
    description: "Loại agent tùy chỉnh của người dùng",
    config: {
      enableAgentProfileCustomization: true,
      enableOutputToMessenger: true,
      enableOutputToWebsiteLiveChat: true,
      enableOutputToZaloOA: true,
      enableTaskConversionTracking: false,
      enableResourceUsage: true,
      enableDynamicStrategyExecution: false,
      enableMultiAgentCollaboration: false
    },
    groupToolIds: []
  });

  // Xử lý thay đổi input text
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý thay đổi checkbox config
  const handleConfigChange = (configKey: keyof CustomAgentFormData['config']) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [configKey]: !prev.config[configKey]
      }
    }));
  };

  // Xử lý thay đổi group tool được chọn
  const handleGroupToolChange = (selectedIds: number[]) => {
    setFormData(prev => ({
      ...prev,
      groupToolIds: selectedIds
    }));
  };

  // Xử lý khi lưu form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card title="Thông tin cơ bản" className="mb-6">
        <div className="p-4 space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tên Agent
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Nhập tên agent"
              required
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Mô tả
            </label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Nhập mô tả agent"
              rows={3}
              required
            />
          </div>
        </div>
      </Card>

      <Card title="Cấu hình" className="mb-6">
        <div className="p-4 space-y-4">
          <ConfigFieldsGrid
            fields={FULL_TYPE_AGENT_CONFIG_FIELDS}
            config={formData.config}
            onChange={handleConfigChange}
            columns={2}
            descriptionMode="tooltip"
          />
        </div>
      </Card>

      <Card title="Group Tools" className="mb-6">
        <div className="p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Chọn các group tool cho agent này
          </p>

          <GroupToolTable
            groups={sampleGroupTools}
            selectedGroupIds={formData.groupToolIds || []}
            onSelectionChange={handleGroupToolChange}
          />
        </div>
      </Card>

      <div className="flex justify-end space-x-4">
        <Button variant="secondary" type="button" onClick={onCancel}>
          Hủy
        </Button>
        <Button variant="primary" type="submit">
          Lưu
        </Button>
      </div>
    </form>
  );
};

export default CustomAgentForm;
