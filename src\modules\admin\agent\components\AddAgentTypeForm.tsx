import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Checkbox,
} from '@/shared/components/common';
import AgentSystemConfig, { AgentSystemConfigData } from './AgentSystemConfig';
import { z } from 'zod';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';

// Import the correct types from service
import { CreateTypeAgentParams, AgentTypeStatusEnum } from '../agent-type/types/type-agent.types';

interface CreateTypeAgentResponse {
  result?: {
    id: number;
  };
}

interface AddAgentTypeFormProps {
  onSubmit: (values: CreateTypeAgentParams) => Promise<CreateTypeAgentResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAgentTypeSchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.type.validation.nameRequired', 'Tên loại agent là bắt buộc'))
    .trim(),
  description: z.string()
    .min(1, t('admin:agent.type.validation.descriptionRequired', 'Mô tả là bắt buộc'))
    .trim(),
  status: z.nativeEnum(AgentTypeStatusEnum, {
    required_error: t('admin:agent.type.validation.statusRequired', 'Trạng thái là bắt buộc'),
  }),
});

const AddAgentTypeForm: React.FC<AddAgentTypeFormProps> = ({ onSubmit, onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { validationError } = useAdminAgentNotification();
  
  // Default config states
  const [defaultConfig, setDefaultConfig] = useState({
    enableAgentProfileCustomization: true,
    enableOutputToMessenger: true,
    enableOutputToWebsiteLiveChat: true,
    enableTaskConversionTracking: false,
    enableResourceUsage: true,
    enableDynamicStrategyExecution: true,
    enableMultiAgentCollaboration: false,
    enableOutputToZaloOA: true,
  });

  // Agent Systems config state
  const [agentSystemsConfig, setAgentSystemsConfig] = useState<AgentSystemConfigData>({
    agentSystems: [],
  });


  // Default values for the form
  const defaultValues = React.useMemo(() => ({
    name: '',
    description: '',
    status: AgentTypeStatusEnum.DRAFT,
  }), []);

  // Handle default config changes
  const handleDefaultConfigChange = (key: keyof typeof defaultConfig, value: boolean) => {
    setDefaultConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle agent systems config changes
  const handleAgentSystemsConfigChange = (data: AgentSystemConfigData) => {
    setAgentSystemsConfig(data);
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Validate agent systems
      if (agentSystemsConfig.agentSystems.length === 0) {
        validationError(t('admin:agent.type.validation.agentSystemsRequired', 'Ít nhất một agent system là bắt buộc'));
        setIsSubmitting(false);
        return;
      }

      // Prepare form data
      const agentTypeData: CreateTypeAgentParams = {
        name: values['name'] as string,
        description: values['description'] as string,
        defaultConfig,
        status: values['status'] as AgentTypeStatusEnum,
        agentSystems: agentSystemsConfig.agentSystems,
      };

      console.log('Submitting agent type data:', agentTypeData);

      // Submit form data
      const createResult = await onSubmit(agentTypeData);
      console.log('Agent type created successfully:', createResult);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error submitting agent type form:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Status options
  const statusOptions = [
    { value: AgentTypeStatusEnum.DRAFT, label: t('admin:agent.type.form.draft', 'Nháp') },
    { value: AgentTypeStatusEnum.APPROVED, label: t('admin:agent.type.form.approved', 'Đã duyệt') },
  ];

  return (
    <Card>
      <div className="flex justify-start items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.type.addType', 'Thêm Loại Agent mới')}
        </Typography>
      </div>

      <Form
        schema={createAgentTypeSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormItem
            name="name"
            label={t('admin:agent.form.name', 'Tên Loại Agent')}
            required
          >
            <Input
              fullWidth
              placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên loại agent')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('admin:agent.form.description', 'Mô tả')}
            required
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả loại agent')}
            />
          </FormItem>

          <FormGrid columns={1} columnsMd={1} columnsSm={1} gap="md">
            <FormItem
              name="status"
              label={t('admin:agent.form.status', 'Trạng thái')}
              required
            >
              <Select
                fullWidth
                placeholder={t('admin:agent.form.selectStatus', 'Chọn trạng thái')}
                options={statusOptions}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Agent Systems Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.agentSystems', 'Agent Systems')}
          </Typography>

          <AgentSystemConfig
            initialData={agentSystemsConfig}
            onSave={handleAgentSystemsConfigChange}
            mode="create"
          />
        </div>

        <Divider />

        {/* Default Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.defaultConfig', 'Cấu hình mặc định')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Checkbox
              checked={defaultConfig.enableAgentProfileCustomization}
              onChange={(checked) => handleDefaultConfigChange('enableAgentProfileCustomization', checked)}
              label={t('admin:agent.form.enableAgentProfileCustomization', 'Cho phép tùy chỉnh hồ sơ agent')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToMessenger}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToMessenger', checked)}
              label={t('admin:agent.form.enableOutputToMessenger', 'Cho phép xuất ra Messenger')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToWebsiteLiveChat}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToWebsiteLiveChat', checked)}
              label={t('admin:agent.form.enableOutputToWebsiteLiveChat', 'Cho phép xuất ra Website Live Chat')}
            />
            <Checkbox
              checked={defaultConfig.enableTaskConversionTracking}
              onChange={(checked) => handleDefaultConfigChange('enableTaskConversionTracking', checked)}
              label={t('admin:agent.form.enableTaskConversionTracking', 'Theo dõi chuyển đổi tác vụ')}
            />
            <Checkbox
              checked={defaultConfig.enableResourceUsage}
              onChange={(checked) => handleDefaultConfigChange('enableResourceUsage', checked)}
              label={t('admin:agent.form.enableResourceUsage', 'Sử dụng tài nguyên')}
            />
            <Checkbox
              checked={defaultConfig.enableDynamicStrategyExecution}
              onChange={(checked) => handleDefaultConfigChange('enableDynamicStrategyExecution', checked)}
              label={t('admin:agent.form.enableDynamicStrategyExecution', 'Thực thi chiến lược động')}
            />
            <Checkbox
              checked={defaultConfig.enableMultiAgentCollaboration}
              onChange={(checked) => handleDefaultConfigChange('enableMultiAgentCollaboration', checked)}
              label={t('admin:agent.form.enableMultiAgentCollaboration', 'Hợp tác đa agent')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToZaloOA}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToZaloOA', checked)}
              label={t('admin:agent.form.enableOutputToZaloOA', 'Cho phép xuất ra Zalo OA')}
            />
          </div>
        </div>

        <Divider />

        {/* Form Actions */}
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('admin:agent.type.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
          >
            {t('admin:agent.form.create', 'Tạo Loại Agent')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentTypeForm;
