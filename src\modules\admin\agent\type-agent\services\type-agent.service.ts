import { apiClient } from '@/shared/api/axios';
import {
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
  TypeAgentResponse,
  CreateTypeAgentResponse,
  AgentSystemListItem,
} from '../types/type-agent.types';

/**
 * Service để tương tác với API type agent của admin
 */
export class AdminTypeAgentService {
  private baseUrl = '/admin/type-agents';

  /**
   * Lấy danh sách type agents
   * @param params Tham số truy vấn
   * @returns Danh sách type agents
   */
  async getTypeAgents(params: TypeAgentQueryParams): Promise<TypeAgentResponse['result']> {
    try {
      const response = await apiClient.get<TypeAgentResponse['result']>(this.baseUrl, {
        params,
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching type agents:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết type agent theo ID
   * @param id ID của type agent
   * @returns Thông tin chi tiết type agent
   */
  async getTypeAgentById(id: number): Promise<TypeAgentDetail> {
    try {
      const response = await apiClient.get<TypeAgentDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo type agent mới
   * @param data Dữ liệu tạo type agent
   * @returns ID của type agent được tạo
   */
  async createTypeAgent(data: CreateTypeAgentParams): Promise<CreateTypeAgentResponse['result']> {
    try {
      const response = await apiClient.post<CreateTypeAgentResponse['result']>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating type agent:', error);
      throw error;
    }
  }

  /**
   * Cập nhật type agent
   * @param id ID của type agent
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateTypeAgent(id: number, data: UpdateTypeAgentParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa type agent
   * @param id ID của type agent
   * @returns Kết quả xóa
   */
  async deleteTypeAgent(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent systems
   * @returns Danh sách agent systems
   */
  async getAgentSystems(): Promise<AgentSystemListItem[]> {
    try {
      const response = await apiClient.get<{ items: AgentSystemListItem[] }>('/admin/agents/system', {
        tokenType: 'admin',
      });
      return response.result.items;
    } catch (error) {
      console.error('Error fetching agent systems:', error);
      throw error;
    }
  }
}

export const adminTypeAgentService = new AdminTypeAgentService();
