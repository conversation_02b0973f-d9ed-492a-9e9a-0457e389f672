/**
 * Utility functions cho Type Agent
 */

import { CustomAgentFormData } from '../components/agent-add/CustomTypeAgentCard';
import { CreateTypeAgentDto, TypeAgentConfig } from '../types/dto';

/**
 * Interface cho config field item
 */
export interface ConfigFieldItem {
  key: keyof TypeAgentConfig;
  label: string;
  description?: string;
}

/**
 * Mapping các field của TypeAgentConfig với label tiếng Việt
 */
export const TYPE_AGENT_CONFIG_FIELDS: ConfigFieldItem[] = [
  {
    key: 'enableAgentProfileCustomization',
    label: 'Hỗ trợ Profile',
    description: 'Cho phép cấu hình thông tin cá nhân của agent'
  },
  {
    key: 'enableResourceUsage',
    label: 'Hỗ trợ Resources',
    description: 'Cho phép quản lý tài nguyên và file'
  },
  {
    key: 'enableTaskConversionTracking',
    label: 'Hỗ trợ Conversion',
    description: 'Cho phép cấu hình chuyển đổi dữ liệu'
  },
  {
    key: 'enableOutputToWebsiteLiveChat',
    label: 'Hỗ trợ Website Live Chat',
    description: 'Cho phép xuất ra website live chat'
  },
  {
    key: 'enableDynamicStrategyExecution',
    label: 'Hỗ trợ Strategy',
    description: 'Cho phép cấu hình chiến lược xử lý'
  },
  {
    key: 'enableMultiAgentCollaboration',
    label: 'Hỗ trợ Multi Agent',
    description: 'Cho phép cấu hình nhiều agent cùng làm việc'
  },
  {
    key: 'enableOutputToMessenger',
    label: 'Hỗ trợ Facebook Messenger',
    description: 'Cho phép xuất ra Facebook Messenger'
  },
  {
    key: 'enableOutputToZaloOA',
    label: 'Hỗ trợ Zalo OA',
    description: 'Cho phép xuất ra Zalo Official Account'
  }
];

/**
 * Lấy danh sách config fields cho FullTypeAgentConfig (tất cả fields)
 */
export const FULL_TYPE_AGENT_CONFIG_FIELDS: ConfigFieldItem[] = TYPE_AGENT_CONFIG_FIELDS;

/**
 * Chuyển đổi CustomAgentFormData sang CreateTypeAgentDto
 * @param formData Dữ liệu từ form
 * @returns DTO để gửi API
 */
export const convertCustomAgentFormToDto = (
  formData: CustomAgentFormData
): CreateTypeAgentDto => {
  // Chuyển đổi config từ CustomAgentFormData.config sang TypeAgentConfig
  const config: TypeAgentConfig = {
    enableAgentProfileCustomization: formData.config.enableAgentProfileCustomization,
    enableResourceUsage: formData.config.enableResourceUsage,
    enableTaskConversionTracking: formData.config.enableTaskConversionTracking,
    enableOutputToWebsiteLiveChat: formData.config.enableOutputToWebsiteLiveChat,
    enableDynamicStrategyExecution: formData.config.enableDynamicStrategyExecution ?? false,
    enableMultiAgentCollaboration: formData.config.enableMultiAgentCollaboration ?? false,
    enableOutputToMessenger: formData.config.enableOutputToMessenger ?? true,
    enableOutputToZaloOA: formData.config.enableOutputToZaloOA ?? true,
  };

  return {
    name: formData.name,
    description: formData.description,
    config,
    groupToolIds: formData.groupToolIds || [],
  };
};

/**
 * Chuyển đổi CreateTypeAgentDto sang CustomAgentFormData
 * @param dto DTO từ API
 * @returns Dữ liệu cho form
 */
export const convertDtoToCustomAgentForm = (
  dto: CreateTypeAgentDto
): CustomAgentFormData => {
  return {
    name: dto.name,
    description: dto.description || '',
    config: {
      enableAgentProfileCustomization: dto.config.enableAgentProfileCustomization || false,
      enableResourceUsage: dto.config.enableResourceUsage || false,
      enableTaskConversionTracking: dto.config.enableTaskConversionTracking || false,
      enableOutputToWebsiteLiveChat: dto.config.enableOutputToWebsiteLiveChat || false,
      enableDynamicStrategyExecution: dto.config.enableDynamicStrategyExecution || false,
      enableMultiAgentCollaboration: dto.config.enableMultiAgentCollaboration || false,
      enableOutputToMessenger: dto.config.enableOutputToMessenger || false,
      enableOutputToZaloOA: dto.config.enableOutputToZaloOA || false,
    },
    groupToolIds: dto.groupToolIds,
  };
};

/**
 * Validate dữ liệu form trước khi submit
 * @param formData Dữ liệu form
 * @returns Object chứa isValid và errors
 */
export const validateCustomAgentForm = (formData: CustomAgentFormData) => {
  const errors: Record<string, string> = {};

  // Validate tên
  if (!formData.name.trim()) {
    errors['name'] = 'Tên agent không được để trống';
  } else if (formData.name.trim().length < 2) {
    errors['name'] = 'Tên agent phải có ít nhất 2 ký tự';
  } else if (formData.name.trim().length > 100) {
    errors['name'] = 'Tên agent không được vượt quá 100 ký tự';
  }

  // Validate mô tả
  if (!formData.description.trim()) {
    errors['description'] = 'Mô tả không được để trống';
  } else if (formData.description.trim().length < 10) {
    errors['description'] = 'Mô tả phải có ít nhất 10 ký tự';
  } else if (formData.description.trim().length > 500) {
    errors['description'] = 'Mô tả không được vượt quá 500 ký tự';
  }

  // Validate group tools - không bắt buộc
  // Bỏ validation bắt buộc cho groupToolIds

  // Validate config - ít nhất một tính năng phải được bật
  const hasAnyFeature = Object.values(formData.config).some(value => value === true);
  if (!hasAnyFeature) {
    errors['config'] = 'Phải bật ít nhất một tính năng cho agent';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
