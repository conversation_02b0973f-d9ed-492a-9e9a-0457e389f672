import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminAgentStrategyService } from '../services/agent-strategy.service';
import {
  AgentStrategyQueryParams,
  AgentStrategyDetail,
  CreateAgentStrategyParams,
  UpdateAgentStrategyParams,
} from '../types/agent-strategy.types';

/**
 * Query keys cho agent strategy
 */
export const ADMIN_AGENT_STRATEGY_QUERY_KEYS = {
  all: ['admin', 'agent-strategy'] as const,
  lists: () => [...ADMIN_AGENT_STRATEGY_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentStrategyQueryParams) => [...ADMIN_AGENT_STRATEGY_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_STRATEGY_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_AGENT_STRATEGY_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách agent strategy
 */
export const useAdminAgentStrategies = (params: AgentStrategyQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.list(params),
    queryFn: () => adminAgentStrategyService.getAgentStrategies(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết agent strategy
 */
export const useAdminAgentStrategyDetail = (id: string) => {
  return useQuery<AgentStrategyDetail>({
    queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentStrategyService.getAgentStrategyById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo agent strategy mới
 */
export const useCreateAdminAgentStrategy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentStrategyParams) => adminAgentStrategyService.createAgentStrategy(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent strategy
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent strategy
 */
export const useUpdateAdminAgentStrategy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentStrategyParams }) =>
      adminAgentStrategyService.updateAgentStrategy(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate và refetch danh sách và detail
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.detail(id),
      });
    },
  });
};

/**
 * Hook để xóa agent strategy
 */
export const useDeleteAdminAgentStrategy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentStrategyService.deleteAgentStrategy(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_STRATEGY_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để upload avatar
 */
export const useUploadAgentStrategyAvatar = () => {
  return useMutation({
    mutationFn: ({ file, uploadUrl }: { file: File; uploadUrl: string }) =>
      adminAgentStrategyService.uploadAvatar(file, uploadUrl),
  });
};
