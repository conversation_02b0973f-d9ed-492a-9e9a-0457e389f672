import React from 'react';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentStrategyCard from './AdminAgentStrategyCard';
import { AgentStrategyListItem } from '../agent-strategy/types/agent-strategy.types';

interface AdminAgentStrategyGridProps {
  strategies: AgentStrategyListItem[];
  onEditStrategy?: (strategyId: string) => void;
}

/**
 * Component hiển thị danh sách Agent Strategies dưới dạng grid
 */
const AdminAgentStrategyGrid: React.FC<AdminAgentStrategyGridProps> = ({ strategies, onEditStrategy }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {strategies.map(strategy => (
        <div key={strategy.id} className="h-full">
          <AdminAgentStrategyCard
            strategy={strategy}
            allStrategies={strategies}
            {...(onEditStrategy && { onEditStrategy })}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default AdminAgentStrategyGrid;
