// Export specific items from agent-system to avoid conflicts
export type {
  AgentSystemListItem,
  AgentSystemDetail,
  CreateAgentSystemParams,
  UpdateAgentSystemParams,
  AgentSystemQueryParams,
  ModelConfig as AgentSystemModelConfig,
  SortDirection as AgentSystemSortDirection,
} from './agent-system';

export {
  AgentStatusEnum,
  AgentSystemSortBy,
  adminAgentSystemService,
  useAdminAgentSystems,
  useAdminAgentSystemDetail,
  useCreateAdminAgentSystem,
  useUpdateAdminAgentSystem,
  useDeleteAdminAgentSystem,
  createAgentSystemSchema,
  updateAgentSystemSchema,
  agentSystemQuerySchema,
  modelConfigSchema as agentSystemModelConfigSchema,
} from './agent-system';

// Export specific items from agent-strategy to avoid conflicts
export type {
  AgentStrategyListItem,
  AgentStrategyDetail,
  CreateAgentStrategyParams,
  UpdateAgentStrategyParams,
  AgentStrategyQueryParams,
  ModelConfig as AgentStrategyModelConfig,
  SortDirection as AgentStrategySortDirection,
} from './agent-strategy';

export {
  AgentStrategyStatusEnum,
  AgentStrategySortBy,
  adminAgentStrategyService,
  useAdminAgentStrategies,
  useAdminAgentStrategyDetail,
  useCreateAdminAgentStrategy,
  useUpdateAdminAgentStrategy,
  useDeleteAdminAgentStrategy,
  createAgentStrategySchema,
  updateAgentStrategySchema,
  agentStrategyQuerySchema,
  modelConfigSchema as agentStrategyModelConfigSchema,
} from './agent-strategy';

// Export agent-rank
export * from './agent-rank';

// Export components and pages
export * from './components';
export * from './pages';
export * from './routers/adminAgentRoutes';

// Export agent-type with specific naming to avoid conflicts
export type {
  TypeAgentListItem,
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
  DefaultConfig,
  TypeAgentResponse,
  CreateTypeAgentResponse,
  SortDirection as TypeAgentSortDirection,
} from './agent-type';

export {
  AgentTypeStatusEnum,
  TypeAgentSortBy,
  adminTypeAgentService,
  useAdminTypeAgents,
  useAdminTypeAgentDetail,
  useCreateAdminTypeAgent,
  useUpdateAdminTypeAgent,
  useDeleteAdminTypeAgent,
  useAgentSystems,
  createTypeAgentSchema,
  updateTypeAgentSchema,
  typeAgentQuerySchema,
  defaultConfigSchema,
} from './agent-type';