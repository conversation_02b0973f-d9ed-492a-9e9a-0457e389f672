import React from 'react';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentTypeCard from './AdminAgentTypeCard';
import { TypeAgentListItem } from '../agent-type/types/type-agent.types';

interface AdminAgentTypeGridProps {
  types: TypeAgentListItem[];
  onEditType?: (typeId: number) => void;
  onSuccess?: () => void;
}

/**
 * Component hiển thị danh sách Admin Agent Types dưới dạng grid
 */
const AdminAgentTypeGrid: React.FC<AdminAgentTypeGridProps> = ({ types, onEditType, onSuccess }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {types.map(type => (
        <div key={type.id} className="h-full">
          <AdminAgentTypeCard
            type={type}
            allTypes={types}
            {...(onEditType && { onEditType })}
            {...(onSuccess && { onSuccess })}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default AdminAgentTypeGrid;
