import React, { useState } from 'react';
import { Modal, Button, Select, Typography, IconCard } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { TypeAgentListItem, AgentTypeStatusEnum } from '../agent-type/types/type-agent.types';
import { useDeleteAdminTypeAgent, useDeleteAdminTypeAgentWithMigration, useAdminTypeAgents } from '../agent-type/hooks/useTypeAgent';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';

interface DeleteTypeAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  typeAgent: TypeAgentListItem;
  onSuccess?: () => void;
}

/**
 * Modal xóa Type Agent với tùy chọn migration
 */
const DeleteTypeAgentModal: React.FC<DeleteTypeAgentModalProps> = ({
  isOpen,
  onClose,
  typeAgent,
  onSuccess
}) => {
   const { t } = useTranslation(['admin', 'common']);
  const {
    deleteSuccess,
    deleteError
  } = useAdminAgentNotification();

  const [selectedNewTypeId, setSelectedNewTypeId] = useState<number | null>(null);
  
  // Hooks cho API calls
  const deleteTypeMutation = useDeleteAdminTypeAgent();
  const deleteWithMigrationMutation = useDeleteAdminTypeAgentWithMigration();

  // Lấy danh sách type agents để chọn (loại bỏ type đang xóa)
  const { data: typesResponse } = useAdminTypeAgents({
    page: 1,
    limit: 100,
    search: ''
  });

  // Lọc ra type đang xóa và chỉ lấy những type có status APPROVED
  const availableTypes = typesResponse?.items?.filter(type =>
    type.id !== typeAgent.id && type.status === AgentTypeStatusEnum.APPROVED
  ) || [];

 

  const handleDeleteWithMigration = async () => {
    if (!selectedNewTypeId) {
      deleteError(
        t('admin:agent.type.pageTitle', 'Loại Agent'),
        t('admin-agent:type.selectNewType', 'Select new agent type')
      );
      return;
    }

    try {
      await deleteWithMigrationMutation.mutateAsync({
        id: typeAgent.id,
        newTypeAgentId: selectedNewTypeId
      });

      deleteSuccess(t('admin:agent.type.pageTitle', 'Loại Agent'));

      onClose();
      onSuccess?.();
    } catch (err) {
      console.error('Error deleting type agent with migration:', err);
      deleteError(
        t('admin:agent.type.pageTitle', 'Loại Agent'),
        err instanceof Error ? err.message : t('admin-agent:type.deleteError', 'An error occurred while deleting agent type')
      );
    }
  };

  const handleCancel = () => {
    setSelectedNewTypeId(null);
    onClose();
  };

  const isLoading = deleteTypeMutation.isPending || deleteWithMigrationMutation.isPending;

  // Format ngày tạo
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(parseInt(dateString));
      // Sử dụng locale từ admin namespace
      const locale = t('admin-agent:common.locale', 'en-US');
      return date.toLocaleDateString(locale);
    } catch {
      return dateString;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title={t('admin-agent:type.deleteConfirmTitle', 'Confirm Delete Agent Type')}
      size="lg"
      footer={
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
          >
            {t('admin-agent:common.cancel', 'Cancel')}
          </Button>
        
          <Button
            variant="primary"
            onClick={handleDeleteWithMigration}
            isLoading={deleteWithMigrationMutation.isPending}
            disabled={deleteTypeMutation.isPending || !selectedNewTypeId}
          >
            {t('admin-agent:type.deleteWithMigration', 'Delete with Migration')}
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Thông tin loại agent sẽ bị xóa */}
        <div>
          <Typography variant="h6" className="mb-3">
            {t('admin-agent:type.selectTypeToDelete', 'Select Agent Type to Delete')}
          </Typography>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {t('admin-agent:type.deleteConfirmMessage', 'Are you sure you want to delete this agent type? This action will move the agent type to trash and can be restored.')}
          </p>
          
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <IconCard
                  icon="settings"
                  variant="default"
                  size="sm"
                  className="text-white"
                />
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{typeAgent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {typeAgent.status === AgentTypeStatusEnum.APPROVED 
                    ? t('admin:agent.form.approved') 
                    : t('admin:agent.form.draft')
                  } • {formatDate(typeAgent.createdAt)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {typeAgent.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tùy chọn chuyển đổi */}
        <div>
          <Typography variant="h6" className="mb-3">
            {t('admin:agent.type.deleteWithMigration', 'Xóa với chuyển đổi')}
          </Typography>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {t('admin:agent.type.deleteWithMigrationDescription', 'Xóa loại agent và chuyển tất cả agents thuộc loại này sang loại mới được chọn.')}
          </p>
          
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('admin:agent.type.newTypeAgent', 'Loại agent mới')}
            </label>
            <div
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => e.stopPropagation()}
              onTouchStart={(e) => e.stopPropagation()}
            >
              <Select
                value={selectedNewTypeId?.toString() || ''}
                onChange={(value) => setSelectedNewTypeId(value ? parseInt(value as string) : null)}
                placeholder={t('admin:agent.selectNewType', 'Chọn loại agent mới')}
                disabled={isLoading}
                options={availableTypes.map((type) => ({
                  value: type.id.toString(),
                  label: type.name,
                  data: { status: type.status }
                }))}
                renderOption={(option) => (
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
                      <IconCard
                        icon="settings"
                        variant="default"
                        size="sm"
                        className="text-white"
                      />
                    </div>
                    <span>{option.label}</span>
                    <span className="text-xs text-gray-500 ">
                      ({option.data?.['status'] === AgentTypeStatusEnum.APPROVED
                        ? t('admin:agent.form.approved')
                        : t('admin:agent.form.draft')
                      })
                    </span>
                  </div>
                )}
              />
            </div>
            {availableTypes.length === 0 ? (
              <p className="text-sm text-amber-600 dark:text-amber-400">
                {t('admin:agent.type.noAvailableTypes', 'Không có loại agent nào khác để chuyển đổi. Bạn chỉ có thể xóa mà không chuyển đổi.')}
              </p>
            ) : (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {t('admin:agent.type.selectNewTypeDescription', 'Chọn loại agent mới để chuyển đổi các agents hiện tại.')}
              </p>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteTypeAgentModal;
