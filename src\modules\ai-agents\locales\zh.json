{"aiAgents": {"notification": {"createSuccess": "{{entityName}}已成功创建", "updateSuccess": "{{entityName}}已成功更新", "deleteSuccess": "{{entityName}}已成功删除", "copySuccess": "{{entityName}}已成功复制", "shareSuccess": "{{entityName}}已成功分享", "createError": "创建{{entityName}}时发生错误", "updateError": "更新{{entityName}}时发生错误", "deleteError": "删除{{entityName}}时发生错误", "copyError": "复制{{entityName}}时发生错误", "shareError": "分享{{entityName}}时发生错误", "loadError": "无法加载{{entityName}}列表", "uploadSuccess": "上传成功", "uploadSuccessWithName": "{{fileName}}上传成功", "uploadError": "上传时发生错误", "validationError": "数据无效", "permissionError": "您没有权限执行此操作", "networkError": "网络连接错误。请重试", "processing": "正在{{action}}...", "saveSuccess": "保存成功", "saveError": "保存时发生错误", "publishSuccess": "{{entityName}}已成功发布", "publishError": "发布{{entityName}}时发生错误", "restoreSuccess": "{{entityName}}已成功恢复", "restoreError": "恢复{{entityName}}时发生错误"}, "common": {"save": "保存", "cancel": "取消", "add": "添加", "edit": "编辑", "delete": "删除", "search": "搜索", "filter": "筛选", "sort": "排序", "required": "必填", "update": "更新", "create": "创建", "select": "选择", "configure": "配置", "name": "名称", "description": "描述", "type": "类型", "status": "状态", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作", "noData": "暂无数据", "loading": "加载中...", "error": "发生错误", "success": "成功", "confirm": "确认", "confirmDelete": "确定要删除吗？", "yes": "是", "no": "否", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "finish": "完成", "agentDetail": "智能助手详情"}, "aiAgents": "AI 代理", "agentCreate": {"title": "创建智能助手", "customAgentButton": "创建自定义智能助手", "selectAgentType": "选择智能助手类型", "selectAgentDescription": "从下面的列表中选择一种智能助手类型，或创建您自己的自定义智能助手。", "configureAgent": "配置 {name}", "sortBy": "排序方式", "sortName": "名称", "sortDate": "创建日期", "order": "顺序", "orderAsc": "升序", "orderDesc": "降序", "agentTypeDescription": "选择适合您需求的智能助手类型。每种智能助手类型都有不同的功能和特点。"}, "integrationConfig": {"title": "集成", "facebook": "Facebook", "website": "网站", "addFacebook": "添加 Facebook 集成", "addWebsite": "添加网站集成", "noFacebookIntegration": "暂无 Facebook 集成", "noWebsiteIntegration": "暂无网站集成", "selectFacebook": "选择 Facebook 页面", "selectWebsite": "选择网站", "facebookPageName": "Facebook 页面名称", "websiteName": "网站名称", "websiteUrl": "网站 URL"}, "strategyConfig": {"title": "策略", "selectStrategy": "为您的智能助手选择策略", "selectStrategyDescription": "选择一个策略并配置处理步骤", "basicStrategy": "基础策略", "basicStrategyDescription": "带有默认设置的简单策略", "advancedStrategy": "高级策略", "advancedStrategyDescription": "带有高级选项和复杂处理的策略", "customStrategy": "自定义策略", "customStrategyDescription": "创建您自己的完全自定义设置的策略", "configureStrategy": "配置策略", "step": "步骤 {number}", "input": "输入"}, "convertConfig": {"title": "转换配置", "configureFields": "配置要收集的数据字段", "configureFieldsDescription": "选择智能助手将从用户那里收集的数据字段", "noFields": "尚未配置数据字段", "addField": "添加新字段", "editField": "编辑字段", "fieldName": "字段名称", "fieldDescription": "描述", "fieldType": "数据类型", "fieldRequired": "必填字段", "fieldNamePlaceholder": "输入字段名称（例如：email）", "fieldDescriptionPlaceholder": "输入字段描述（例如：收集用户的所有电子邮件）", "pleaseEnterAllFields": "请输入所有字段信息", "text": "文本", "email": "电子邮件", "phone": "电话", "number": "数字", "date": "日期", "address": "地址", "name": "姓名"}, "responseConfig": {"title": "响应资源", "configureResources": "为智能助手配置响应资源", "configureResourcesDescription": "选择智能助手可以用来响应用户的资源", "media": "媒体资源", "url": "URL 资源", "product": "产品资源", "noMedia": "暂无媒体资源", "noUrl": "暂无 URL 资源", "noProduct": "暂无产品资源", "selectMedia": "选择媒体", "selectUrl": "选择 URL", "selectProduct": "选择产品"}, "editAgent": {"title": "编辑智能助手", "subtitle": "更新智能助手信息和配置", "basicInfo": "基本信息", "agentName": "助手名称", "agentNamePlaceholder": "请输入助手名称", "agentDescription": "助手描述", "agentDescriptionPlaceholder": "请输入助手描述", "agentAvatar": "头像", "changeAvatar": "更换头像", "agentStatus": "状态", "active": "活跃", "inactive": "非活跃", "agentLevel": "等级", "agentExp": "经验值", "agentModel": "AI 模型", "agentType": "助手类型", "saveChanges": "保存更改", "cancelEdit": "取消编辑", "deleteAgent": "删除助手", "confirmDelete": "您确定要删除此智能助手吗？", "deleteWarning": "此操作无法撤销。", "updateSuccess": "智能助手更新成功！", "updateError": "智能助手更新失败！", "deleteSuccess": "智能助手删除成功！", "deleteError": "智能助手删除失败！", "validation": {"nameRequired": "助手名称为必填项", "nameMinLength": "助手名称至少需要2个字符", "nameMaxLength": "助手名称不能超过100个字符", "descriptionMaxLength": "描述不能超过500个字符"}}, "mediaSlideInForm": {"title": "选择媒体", "close": "关闭", "cancel": "取消", "save": "保存", "media": "媒体", "type": "类型", "createdAt": "创建日期", "preview": "预览", "view": "查看", "sortBy": "排序方式", "name": "名称", "size": "大小", "filterBy": "筛选方式", "all": "全部", "cannotSaveInThisMode": "无法在此模式下保存。", "updateMediaSuccess": "媒体更新成功！", "updateMediaError": "更新媒体时发生错误。", "addMediaToListSuccess": "媒体已添加到列表！", "loadingFacebookPages": "正在加载 Facebook 页面...", "errorLoadingFacebookPages": "加载 Facebook 页面时出错", "noFacebookIntegrations": "暂无 Facebook 集成", "loadingWebsites": "正在加载网站...", "errorLoadingWebsites": "加载网站时出错", "noWebsiteIntegrations": "暂无网站集成"}, "facebookSlideInForm": {"title": "选择 Facebook 页面", "close": "关闭", "cancel": "取消", "save": "保存", "facebookPage": "Facebook 页面", "category": "类别", "followers": "关注者", "status": "状态", "connected": "已连接", "notConnected": "未连接", "sortBy": "排序方式", "name": "名称", "filterBy": "筛选方式", "all": "全部", "cannotSaveInThisMode": "无法在此模式下保存。", "integratedSuccessfully": "成功集成 {{count}} 个 Facebook 页面！", "cannotIntegrate": "{{count}} 个 Facebook 页面无法集成：{{details}}", "noPageIntegratedSuccessfully": "没有 Facebook 页面集成成功！", "updateIntegrationError": "更新 Facebook 集成时发生错误。", "selectedFacebookIntegration": "已选择 Facebook 集成！", "connectedPages": "已连接", "notConnectedPages": "未连接"}, "websiteSlideInForm": {"title": "选择网站", "close": "关闭", "cancel": "取消", "save": "保存", "website": "网站", "category": "类别", "status": "状态", "connected": "已连接", "processing": "处理中", "connectionError": "连接错误", "notConnected": "未连接", "sortBy": "排序方式", "host": "主机", "createdDate": "创建日期", "filterBy": "筛选方式", "all": "全部", "verified": "已验证", "notVerified": "未验证", "addNewWebsite": "添加新网站", "websiteName": "网站名称", "url": "URL", "enterWebsiteName": "输入网站名称", "enterWebsiteUrl": "https://example.com", "add": "添加", "cannotSaveInThisMode": "无法在此模式下保存。", "integratedSuccessfully": "成功集成 {{count}} 个网站！", "cannotIntegrate": "{{count}} 个网站无法集成：{{details}}", "noWebsiteIntegratedSuccessfully": "没有网站集成成功！", "updateIntegrationError": "更新网站集成时发生错误。", "selectedWebsiteIntegration": "已选择网站集成！", "pleaseEnterAllInfo": "请输入完整的网站信息", "invalidUrl": "无效的 URL。请输入正确的 URL 格式（例如：https://example.com）", "addWebsiteSuccess": "网站添加成功！", "addWebsiteError": "添加网站时发生错误"}, "integration": {"title": "集成", "connectAgentToPlatforms": "将智能助手连接到平台", "connectDescription": "将智能助手连接到 Facebook 页面和网站以与用户互动", "connectAgentToWebsite": "将智能助手连接到网站", "websiteDescription": "将智能助手连接到网站以与用户互动", "connectAgentToFacebook": "将智能助手连接到 Facebook", "facebookDescription": "将智能助手连接到 Facebook 页面以与用户互动", "facebookIntegration": "Facebook 集成", "websiteIntegration": "网站集成", "facebookPages": "Facebook 页面", "websites": "网站", "add": "添加 Website", "confirmDeleteIntegration": "您确定要从智能助手中移除集成 \"{{integrationName}}\" 吗？", "deleteIntegrationWarning": "此操作无法撤销。", "removeFacebookSuccess": "Facebook 集成移除成功！", "removeFacebookError": "移除 Facebook 集成时发生错误。", "removeWebsiteSuccess": "网站集成移除成功！", "removeWebsiteError": "移除网站集成时发生错误。"}, "multiAgent": {"title": "多智能助手配置", "description": "配置多个智能助手协同工作。每个智能助手将具有独立的角色和功能。", "addAgent": "添加智能助手", "agentList": "智能助手列表 ", "noAgents": "暂无智能助手", "addFirstAgent": "添加第一个智能助手以开始多智能助手配置", "moveUp": "上移", "moveDown": "下移", "editDescription": "编辑描述", "deleteAgent": "删除智能助手", "save": "保存", "cancel": "取消", "enterDescription": "输入智能助手描述...", "confirmDeleteAgent": "您确定要移除智能助手 \"{{agentName}}\" 吗？", "deleteAgentWarning": "此操作无法撤销。", "addNewAgent": "添加新智能助手", "selectAgentType": "选择智能助手类型", "selectAgent": "选择一个智能助手...", "customDescription": "自定义描述（可选）", "enterCustomDescription": "为此智能助手输入自定义描述...", "useDefaultDescription": "如果留空，将使用智能助手类型的默认描述", "promptForAgent": "智能助手提示", "enterPrompt": "为此智能助手输入提示...", "required": "*"}, "agentCreatePage": {"title": "创建智能助手", "selectAgentType": "选择智能助手类型", "agentTypeDescription": "选择适合您需求的智能助手类型。每种智能助手类型都有不同的功能和特点。", "allTypes": "所有类型", "systemAgents": "系统智能助手", "userAgents": "用户智能助手", "sortByName": "按名称排序", "sortByDate": "按日期排序", "ascending": "升序", "descending": "降序", "resetFilters": "重置筛选", "errorLoadingAgents": "加载智能助手类型列表时发生错误", "retry": "重试"}, "modelConfig": {"title": "模型配置", "provider": "提供商", "keyLlm": "Key LLM", "selectKeyLlm": "选择 Key LLM", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "instruction": "指令", "instructionPlaceholder": "输入模型指令...", "advancedSettings": "高级设置", "maxTokens": "最大令牌数", "maxTokensDescription": "每次 API 调用的最大令牌数", "temperature": "温度", "temperatureDescription": "结果随机性程度 (0-2)", "topP": "Top P", "topPDescription": "令牌选择的累积概率 (0-1)", "topK": "Top K", "topKDescription": "考虑的最高概率令牌数量", "loading": "加载中...", "errorLoadingModel": "加载模型错误", "noModel": "无可用模型"}, "customToolConfig": {"title": "工具配置", "description": "选择智能助手可以使用的工具", "availableTools": "可用工具", "selectedTools": "已选工具", "noToolsSelected": "未选择工具", "selectTools": "选择工具", "toolName": "工具名称", "toolDescription": "描述", "addTool": "添加工具", "removeTool": "移除工具"}, "toolSlideInForm": {"title": "选择工具", "close": "关闭", "cancel": "取消", "save": "保存", "tool": "工具", "description": "描述", "category": "类别", "status": "状态", "active": "活跃", "inactive": "非活跃", "sortBy": "排序方式", "name": "名称", "filterBy": "筛选方式", "all": "全部", "updateToolsSuccess": "工具更新成功！", "updateToolsError": "更新工具时发生错误。", "addToolsToListSuccess": "工具已添加到列表！"}, "profileConfig": {"title": "个人信息", "name": "姓名", "birthDate": "出生日期", "gender": "性别", "language": "语言", "education": "教育程度", "country": "国家", "position": "职位", "skills": "技能", "personality": "性格", "avatar": "头像", "male": "男", "female": "女", "other": "其他", "highSchool": "高中", "college": "大专", "university": "本科", "postgraduate": "研究生", "addSkill": "添加技能", "addPersonality": "添加性格特点", "skillPlaceholder": "输入标签并按回车", "personalityPlaceholder": "输入标签并按回车", "namePlaceholder": "输入智能助手名称", "positionPlaceholder": "输入职位", "personalityTextPlaceholder": "输入性格描述"}, "agentConfigurationForm": {"createAgent": "创建智能助手", "editAgent": "编辑智能助手"}, "resourcesConfig": {"title": "资源配置", "description": "选择智能助手可以使用的资源", "media": "媒体", "urls": "链接", "products": "产品", "addMedia": "添加媒体", "addUrl": "添加链接", "addProduct": "添加产品", "noMediaSelected": "未选择媒体", "noUrlsSelected": "未选择链接", "noProductsSelected": "未选择产品", "selectedItems": "已选择 {{count}} 项"}, "urlSlideInForm": {"title": "标题", "close": "关闭", "cancel": "取消", "save": "保存", "url": "链接", "description": "描述", "category": "类别", "status": "状态", "lastChecked": "最后检查", "active": "活跃", "broken": "损坏", "pending": "待处理", "sortBy": "排序方式", "filterBy": "筛选方式", "all": "全部", "updateUrlsSuccess": "链接更新成功！", "updateUrlsError": "更新链接时发生错误。", "addUrlsToListSuccess": "链接已添加到列表！"}, "productSlideInForm": {"title": "选择产品", "close": "关闭", "cancel": "取消", "save": "保存", "product": "产品", "name": "名称", "price": "价格", "category": "类别", "status": "状态", "stock": "库存", "inStock": "有库存", "outOfStock": "缺货", "discontinued": "停产", "sortBy": "排序方式", "filterBy": "筛选方式", "all": "全部", "updateProductsSuccess": "产品更新成功！", "updateProductsError": "更新产品时发生错误。", "addProductsToListSuccess": "产品已添加到列表！"}, "agentEditPage": {"title": "编辑智能助手", "loading": "正在加载智能助手信息...", "notFound": "未找到智能助手", "error": "加载智能助手信息时发生错误"}, "zaloOfficialAccountConfig": {"title": "Zalo官方账号", "description": "选择要与您的智能体集成的Zalo官方账号", "addAccount": "添加Zalo账号", "zaloAccounts": "Zalo账号", "noAccountsSelected": "未选择任何账号", "selectedAccounts": "已选择的账号", "accountCount": "{{count}} 个账号", "selectAccounts": "选择Zalo账号", "searchPlaceholder": "搜索账号...", "oaId": "OA ID", "name": "名称", "status": "状态", "createdAt": "创建时间", "avatar": "头像", "active": "活跃", "inactive": "非活跃", "selectAll": "全选", "deselectAll": "取消全选", "selected": "已选择", "save": "保存", "cancel": "取消", "loading": "加载中...", "error": "加载账号列表时出错", "retry": "重试", "noAccountsFound": "未找到任何账号", "updateAccountsSuccess": "Zalo账号更新成功！", "updateAccountsError": "更新Zalo账号时出错。", "addAccountsToListSuccess": "Zalo账号已添加到列表！", "removeAccount": "删除账号", "removeAccountSuccess": "Zalo账号删除成功！", "removeAccountError": "删除Zalo账号时出错。", "confirmDeleteAccount": "您确定要从智能体中删除Zalo账号\"{{accountName}}\"吗？", "deleteAccountWarning": "此操作无法撤销。"}, "zaloSlideInForm": {"title": "选择Zalo官方账号", "close": "关闭", "account": "账号", "status": "状态", "active": "活跃", "inactive": "非活跃", "createdAt": "创建时间", "filterBy": "筛选", "all": "全部", "cancel": "取消", "save": "保存", "addAccountsToListSuccess": "Zalo账号添加成功！", "updateAccountsSuccess": "Zalo账号更新成功！", "updateAccountsError": "更新Zalo账号时出错。", "cannotSaveInThisMode": "无法在此模式下保存"}}}