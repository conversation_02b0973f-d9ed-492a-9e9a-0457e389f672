import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Card,
  Icon,
  IconCard,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useProducts } from '@/modules/business/hooks/useProductQuery';
import { ProductQueryParams, HasPriceDto } from '@/modules/business/types/product.types';
import { Product } from '@/modules/ai-agents/types/response';
import { useAgentProducts, useAddProducts } from '@/modules/ai-agents/hooks/useAgentResources';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component ProductSlideInForm
 */
interface ProductSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * Callback khi có tài nguyên được thêm (chỉ dùng cho mode create)
   */
  onResourceAdded?: (items: Product[]) => void;

  /**
   * Danh sách items đã chọn ban đầu (chỉ dùng cho mode create)
   */
  initialSelectedItems?: Product[];
}

/**
 * Component form trượt để chọn các sản phẩm
 */
const ProductSlideInForm: React.FC<ProductSlideInFormProps> = ({
  isVisible,
  onClose,
  agentId,
  mode,
  onResourceAdded,
  initialSelectedItems = [],
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    updateSuccess,
    updateError,
    validationError,
    info,
    success
  } = useAiAgentNotification();

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  // const [hasChanges, setHasChanges] = useState<boolean>(false);

  // API hooks
  const { data: agentProductsResponse } = useAgentProducts(agentId && mode === 'edit' ? agentId : '');
  const addProductsMutation = useAddProducts();

  // Lấy danh sách products đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedProductIds = React.useMemo(() => {
    return (agentProductsResponse?.items?.map(item => Number(item.id))) || [];
  }, [agentProductsResponse?.items]);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: 'name',
    sortDirection: 'ASC',
  });

  // Khởi tạo selectedIds từ agent products hoặc initialSelectedItems
  useEffect(() => {
    if (mode === 'edit' && selectedProductIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedProductIds.map(id => Number(id)));
    } else if (mode === 'create' && initialSelectedItems.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedItems.map(item => Number(item.id)));
    }
  }, [selectedProductIds, mode, selectedIds.length, initialSelectedItems]);

  // API hook để lấy danh sách products
  const { data: productResponse, isLoading, error } = useProducts(queryParams);

  // Debug API response
  console.log('🔍 ProductSlideInForm API Debug:', {
    queryParams,
    productResponse,
    isLoading,
    error,
    timestamp: new Date().toISOString()
  });

  // Lấy dữ liệu từ API response và chuyển đổi sang format Product
  const products: Product[] = (productResponse?.items || []).map(item => ({
    id: item.id,
    name: item.name,
    sku: `PROD-${item.id}`,
    price: (item.price as HasPriceDto)?.listPrice || 0,
    salePrice: (item.price as HasPriceDto)?.salePrice || 0,
    imageUrl: item.images?.[0]?.url || '',
    category: item.tags?.[0] || '',
    stock: 0, // API không có thông tin stock
    status: 'active' as const, // Mặc định là active
    createdAt: new Date(item.createdAt * 1000).toISOString().split('T')[0] || '',
  }));

  const totalItems = productResponse?.meta?.totalItems || 0;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Product>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'product',
      title: t('aiAgents:productSlideInForm.product'),
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.imageUrl ? (
              <img
                src={record.imageUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon name="box" size="md" className="text-gray-500" />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              SKU: {record.sku}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      title: t('aiAgents:productSlideInForm.price'),
      dataIndex: 'price',
      width: '15%',
      render: (_, record) => (
        <div>
          {record.salePrice ? (
            <>
              <Typography variant="body2" className="text-red-500 font-medium">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.salePrice)}
              </Typography>
              <Typography variant="caption" className="text-gray-500 line-through">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
              </Typography>
            </>
          ) : (
            <Typography variant="body2" className="font-medium">
              {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
            </Typography>
          )}
        </div>
      ),
    },
    {
      key: 'category',
      title: t('aiAgents:productSlideInForm.category'),
      dataIndex: 'category',
      width: '15%',
    },
    {
      key: 'stock',
      title: t('aiAgents:productSlideInForm.stock'),
      dataIndex: 'stock',
      width: '15%',
      render: (_, record) => (
        <Typography
          variant="body2"
          className={
            record.stock > 10
              ? 'text-green-500'
              : record.stock > 0
              ? 'text-yellow-500'
              : 'text-red-500'
          }
        >
          {record.stock}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('aiAgents:productSlideInForm.status'),
      dataIndex: 'status',
      width: '15%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              {t('aiAgents:productSlideInForm.inStock')}
            </span>
          ) : record.status === 'inactive' ? (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="x-circle" size="sm" className="mr-1" />
              {t('aiAgents:productSlideInForm.discontinued')}
            </span>
          ) : (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              {t('aiAgents:productSlideInForm.outOfStock')}
            </span>
          )}
        </div>
      ),
    },
  ];



  // Kiểm tra có thay đổi chưa lưu không
  // useEffect(() => {
  //   const hasUnsavedChanges = mode === 'edit' && (
  //     selectedIds.length !== selectedProductIds.length ||
  //     selectedIds.some(id => !selectedProductIds.includes(id)) ||
  //     selectedProductIds.some(id => !selectedIds.includes(id))
  //   );

  //   setHasChanges(hasUnsavedChanges || selectedIds.length > 0);
  // }, [selectedIds, selectedProductIds, mode]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: column,
      sortDirection: direction,
    }));
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentId) {
        validationError(t('common:error'));
        return;
      }

      setIsSubmitting(true);
      try {
        // Chỉ gửi các productId mới chưa có trong agent
        const newProductIds = selectedIds.filter(id => !selectedProductIds.includes(id));
        if (newProductIds.length === 0) {
          info({
            message: t('common:info'),
          });
          setIsSubmitting(false);
          onClose();
          return;
        }
        await addProductsMutation.mutateAsync({
          agentId,
          data: { productIds: newProductIds }
        });

        updateSuccess(t('aiAgents:responseConfig.product'));

        onClose();
      } catch (error) {
        updateError(t('aiAgents:responseConfig.product'), error instanceof Error ? error.message : undefined);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách đã chọn
      const selectedProductItems = products.filter(item => selectedIds.includes(Number(item.id)));

      // Đảm bảo productIds là number[] khi truyền về cho parent
      if (onResourceAdded) {
        onResourceAdded(selectedProductItems.map(item => ({ ...item, id: Number(item.id) })));
      }

      success({
        message: t('aiAgents:productSlideInForm.addProductsToListSuccess'),
      });

      onClose();
    }
  };

  // Xử lý đóng form - không cần confirm
  const handleClose = useCallback(() => {
    setQueryParams(prev => ({ ...prev, search: '' }));
    onClose();
  }, [onClose]);



  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: t('aiAgents:productSlideInForm.sortBy'),
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: t('aiAgents:productSlideInForm.name'),
      onClick: () => handleSortChange('name', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-price',
      label: t('aiAgents:productSlideInForm.price'),
      onClick: () => handleSortChange('price', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-stock',
      label: t('aiAgents:productSlideInForm.stock'),
      onClick: () => handleSortChange('stock', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-date',
      label: t('common:createdAt'),
      onClick: () => handleSortChange('createdAt', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider-1',
      divider: true,
    },
    {
      id: 'filter-category',
      label: t('aiAgents:productSlideInForm.category'),
      icon: 'folder',
      onClick: () => { },
    },
    {
      id: 'category-all',
      label: t('aiAgents:productSlideInForm.all'),
      onClick: () => setQueryParams(prev => ({ ...prev, search: '' })),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:productSlideInForm.title')}</Typography>
          <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('aiAgents:productSlideInForm.cancel')}
            onClick={handleClose}
            disabled={isSubmitting}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('aiAgents:productSlideInForm.save')}
            onClick={handleSave}
            disabled={isLoading || isSubmitting || (mode === 'edit' && !agentId) || selectedIds.length === 0}
            isLoading={isSubmitting}
          />
        </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<Product>
            columns={columns}
            data={products}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys.map(key => Number(key))),
            }}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== (queryParams.limit || 10)) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </div>

        {/* Nút lưu */}
        

        {/* Nút lưu */}
     
      </Card>
    </SlideInForm>
  );
};

export default ProductSlideInForm;
