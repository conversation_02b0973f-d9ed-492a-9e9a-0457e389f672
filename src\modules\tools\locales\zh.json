{"tools": {"title": "工具", "description": "管理您的工具", "noTools": "未找到工具", "noDescription": "无描述", "cloneAllPublic": "克隆所有公共工具", "cloning": "克隆中...", "hasUpdate": "有更新", "noUpdate": "无更新", "update": "更新", "activate": "激活", "deactivate": "停用", "updateVersionSuccess": "版本更新成功！", "updateVersionError": "更新版本时发生错误，请重试。", "status": {"draft": "草稿", "approved": "已批准", "deprecated": "已弃用", "pending": "待审核", "rejected": "已拒绝"}, "form": {"name": "工具名称", "description": "描述", "version": "版本", "content": "内容", "schema": "架构", "parameters": "参数", "save": "保存", "cancel": "取消", "edit": "编辑", "create": "创建", "update": "更新", "delete": "删除", "view": "查看", "close": "关闭", "toolDescription": "显示描述", "toolDescriptionPlaceholder": "输入工具显示描述", "parametersPlaceholder": "输入JSON格式的工具参数", "changeDescription": "变更描述", "changeDescriptionPlaceholder": "描述此版本中的变更", "status": "状态", "invalidJson": "无效的JSON格式"}, "actions": {"viewVersions": "查看版本", "updateFromAdmin": "从管理员更新", "rollbackToAdmin": "回滚到管理员版本", "toggleActive": "切换激活状态", "clone": "克隆", "export": "导出", "import": "导入"}, "notFound": "未找到工具", "toolDetails": "工具详情", "name": "工具名称", "default": "默认", "noVersions": "暂无版本", "editToolVersion": "编辑工具版本", "updateFromAdmin": "从管理员更新", "rollback": "回滚到原始版本", "messages": {"createSuccess": "工具创建成功！", "createError": "创建工具时发生错误", "updateSuccess": "工具更新成功！", "updateError": "更新工具时发生错误", "deleteSuccess": "工具删除成功！", "deleteError": "删除工具时发生错误", "activateSuccess": "工具激活成功！", "deactivateSuccess": "工具停用成功！", "toggleError": "更改工具状态时发生错误", "cloneSuccess": "工具克隆成功！", "cloneError": "克隆工具时发生错误", "confirmDelete": "您确定要删除此工具吗？", "confirmDeactivate": "您确定要停用此工具吗？"}, "validation": {"nameRequired": "工具名称是必需的", "nameMinLength": "工具名称至少需要3个字符", "nameMaxLength": "工具名称不能超过100个字符", "descriptionMaxLength": "描述不能超过500个字符", "versionRequired": "版本是必需的", "versionFormat": "版本必须是x.y.z格式", "contentRequired": "内容是必需的", "schemaRequired": "架构是必需的", "schemaInvalid": "架构无效", "parametersInvalid": "参数无效"}, "table": {"name": "名称", "description": "描述", "status": "状态", "version": "版本", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作", "active": "激活", "hasUpdate": "有更新"}, "filters": {"all": "全部", "active": "激活", "inactive": "未激活", "hasUpdate": "有更新", "noUpdate": "无更新", "search": "搜索工具..."}, "versions": {"title": "工具版本", "current": "当前", "latest": "最新", "create": "创建新版本", "edit": "编辑版本", "delete": "删除版本", "activate": "激活版本", "changelog": "更新日志", "releaseNotes": "发布说明"}, "integration": {"title": "工具集成", "description": "描述", "configure": "配置", "test": "测试", "enable": "启用", "disable": "禁用", "settings": "设置", "apiKey": "API密钥", "webhook": "Webhook", "callback": "回调URL", "name": "名称", "baseUrl": "基础URL", "openapiSpec": "OpenAPI规范", "authentication": "身份验证", "authType": "验证类型", "basicInfo": "基本信息", "namePlaceholder": "输入集成名称", "descriptionPlaceholder": "输入集成描述", "baseUrlPlaceholder": "https://api.example.com", "openapiPlaceholder": "粘贴您的OpenAPI规范（JSON格式）", "viewIntegration": "查看集成", "editIntegration": "编辑集成", "createIntegration": "创建集成", "testConnection": "测试连接", "refreshSpec": "刷新规范", "confirmDelete": "确认删除", "confirmDeleteMessage": "您确定要删除此集成吗？此操作无法撤销。", "connectionTestResult": "连接测试结果", "apiKeyConfig": "API密钥配置", "oauthConfig": "OAuth配置", "apiKeyLocation": "位置", "paramName": "参数名称", "paramNamePlaceholder": "例如：X-API-Key", "schemeName": "方案名称", "schemeNamePlaceholder": "例如：ApiKeyAuth", "apiKeyPlaceholder": "输入您的API密钥", "token": "令牌", "tokenPlaceholder": "输入您的OAuth令牌", "tokenSource": "令牌来源", "auth": {"none": "无身份验证", "apiKey": "API密钥", "oauth": "OAuth"}, "table": {"name": "名称", "endpoint": "端点", "method": "方法", "authType": "验证类型", "status": "状态", "createdAt": "创建时间"}, "validation": {"invalidOpenApiFormat": "无效的OpenAPI格式。必须包含openapi、info和paths属性。", "invalidJson": "无效的JSON格式", "openapiRequired": "需要有效的OpenAPI规范", "invalidUrl": "无效的URL格式", "apiKeyRequired": "API密钥是必需的", "paramNameRequired": "参数名称是必需的", "tokenRequired": "令牌是必需的"}}, "management": {"title": "工具管理", "overview": "概览", "statistics": "统计", "usage": "使用情况", "performance": "性能", "logs": "日志", "monitoring": "监控"}}}