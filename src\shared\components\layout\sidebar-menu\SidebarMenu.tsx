import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Tooltip, ScrollArea } from '@/shared/components/common';
import symbolImage from '@/shared/assets/images/symbol.png';

interface SidebarMenuItem {
  icon: React.ReactNode;
  tooltip: string;
  onClick?: () => void;
  path?: string;
}

interface SidebarMenuProps {
  items: SidebarMenuItem[];
  onOpenChat: () => void;
}

const SidebarMenu: React.FC<SidebarMenuProps> = ({ items, onOpenChat }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleItemClick = (item: SidebarMenuItem) => {
    if (item.onClick) {
      item.onClick();
    } else if (item.path) {
      navigate(item.path);
    }
  };

  return (
    <div className="w-16 h-full bg-white dark:bg-dark border-r border-gray-200 dark:border-gray-700 flex flex-col items-center py-4 flex-shrink-0">
      {/* Logo/Chat button */}
      <button
        className="p-2 rounded-full bg-primary text-white hover:bg-primary/90 mb-6 shadow-md"
        onClick={onOpenChat}
      >
        <img src={symbolImage} alt="RedAI Symbol" className="w-6 h-6" />
      </button>

      {/* Menu items - Sử dụng ScrollArea cho scroll thống nhất */}
      <ScrollArea className="flex-1 w-full" autoHide invisible>
        <div className="flex flex-col items-center space-y-4 w-full">
          {items.map((item, index) => (
            <Tooltip key={index} content={t(item.tooltip)} position="right" className="zIndex-1000">
              <button
                className="p-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-lighter text-gray-700 dark:text-gray-200 transition-colors duration-200 w-full flex justify-center"
                onClick={() => handleItemClick(item)}
              >
                {item.icon}
              </button>
            </Tooltip>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default SidebarMenu;
