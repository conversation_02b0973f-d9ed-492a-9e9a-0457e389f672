import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminTypeAgentService } from '../services/type-agent.service';
import {
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
  AgentSystemListItem,
} from '../types/type-agent.types';

// Query keys
export const ADMIN_TYPE_AGENT_QUERY_KEYS = {
  all: ['admin', 'type-agent'] as const,
  lists: () => [...ADMIN_TYPE_AGENT_QUERY_KEYS.all, 'list'] as const,
  list: (params: TypeAgentQueryParams) => [...ADMIN_TYPE_AGENT_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_TYPE_AGENT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ADMIN_TYPE_AGENT_QUERY_KEYS.details(), id] as const,
  agentSystems: () => [...ADMIN_TYPE_AGENT_QUERY_KEYS.all, 'agent-systems'] as const,
};

/**
 * Hook để lấy danh sách type agents
 */
export const useAdminTypeAgents = (params: TypeAgentQueryParams) => {
  return useQuery({
    queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.list(params),
    queryFn: () => adminTypeAgentService.getTypeAgents(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết type agent
 */
export const useAdminTypeAgentDetail = (id: number) => {
  return useQuery<TypeAgentDetail>({
    queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.detail(id),
    queryFn: () => adminTypeAgentService.getTypeAgentById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo type agent mới
 */
export const useCreateAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTypeAgentParams) => adminTypeAgentService.createTypeAgent(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách type agents
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật type agent
 */
export const useUpdateAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTypeAgentParams }) =>
      adminTypeAgentService.updateTypeAgent(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết type agent
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa type agent
 */
export const useDeleteAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminTypeAgentService.deleteTypeAgent(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách type agents
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để lấy danh sách agent systems
 */
export const useAgentSystems = () => {
  return useQuery<AgentSystemListItem[]>({
    queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.agentSystems(),
    queryFn: () => adminTypeAgentService.getAgentSystems(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
