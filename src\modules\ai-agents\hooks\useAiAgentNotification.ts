import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Utility function để extract error message từ API response
 */


/**
 * Interface cho notification options
 */
export interface AiAgentNotificationOptions {
  title?: string;
  message: string;
  duration?: number;
}

/**
 * Hook để hiển thị notification với đa ngôn ngữ cho AI Agents module
 */
export const useAiAgentNotification = () => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const { success, error: showError, warning, info } = useSmartNotification();

  return {
    /**
     * Hiển thị thông báo thành công
     */
    success: (options: AiAgentNotificationOptions) => {
      success({
        title: options.title || t('common:success', 'Thành công'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * <PERSON><PERSON><PERSON> thị thông báo lỗi
     */
    error: (options: AiAgentNotificationOptions) => {
      showError({
        title: options.title || t('common:error', 'Lỗi'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * Hiển thị thông báo cảnh báo
     */
    warning: (options: AiAgentNotificationOptions) => {
      warning({
        title: options.title || t('common:warning', 'Cảnh báo'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    /**
     * Hiển thị thông báo thông tin
     */
    info: (options: AiAgentNotificationOptions) => {
      info({
        title: options.title || t('common:info', 'Thông tin'),
        message: options.message,
        ...(options.duration !== undefined && { duration: options.duration }),
      });
    },

    // Specific notification methods for common AI agent operations
    
    /**
     * Thông báo tạo thành công
     */
    createSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.createSuccess', '{{entityName}} đã được tạo thành công', { entityName }),
      });
    },

    /**
     * Thông báo cập nhật thành công
     */
    updateSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.updateSuccess', '{{entityName}} đã được cập nhật thành công', { entityName }),
      });
    },

    /**
     * Thông báo xóa thành công
     */
    deleteSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.deleteSuccess', '{{entityName}} đã được xóa thành công', { entityName }),
      });
    },

    /**
     * Thông báo sao chép thành công
     */
    copySuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.copySuccess', '{{entityName}} đã được sao chép thành công', { entityName }),
      });
    },

    /**
     * Thông báo chia sẻ thành công
     */
    shareSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.shareSuccess', '{{entityName}} đã được chia sẻ thành công', { entityName }),
      });
    },

    /**
     * Thông báo lỗi tạo - ưu tiên message từ API
     */
    createError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.createError', 'Có lỗi xảy ra khi tạo {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi cập nhật - ưu tiên message từ API
     */
    updateError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.updateError', 'Có lỗi xảy ra khi cập nhật {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi xóa
     */
    deleteError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.deleteError', 'Có lỗi xảy ra khi xóa {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi sao chép
     */
    copyError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.copyError', 'Có lỗi xảy ra khi sao chép {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi chia sẻ
     */
    shareError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.shareError', 'Có lỗi xảy ra khi chia sẻ {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo lỗi tải dữ liệu
     */
    loadError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.loadError', 'Không thể tải danh sách {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo upload thành công
     */
    uploadSuccess: (fileName?: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: fileName 
          ? t('aiAgents:notification.uploadSuccessWithName', 'Tải lên {{fileName}} thành công', { fileName })
          : t('aiAgents:notification.uploadSuccess', 'Tải lên thành công'),
      });
    },

    /**
     * Thông báo upload lỗi
     */
    uploadError: (errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.uploadError', 'Có lỗi xảy ra khi tải lên'),
      });
    },

    /**
     * Thông báo validation lỗi
     */
    validationError: (message: string) => {
      showError({
        title: t('aiAgents:notification.validationError', 'Dữ liệu không hợp lệ'),
        message,
      });
    },

    /**
     * Thông báo không có quyền
     */
    permissionError: () => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: t('aiAgents:notification.permissionError', 'Bạn không có quyền thực hiện thao tác này'),
      });
    },

    /**
     * Thông báo network error
     */
    networkError: () => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: t('aiAgents:notification.networkError', 'Lỗi kết nối mạng. Vui lòng thử lại'),
      });
    },

    /**
     * Thông báo đang xử lý
     */
    processing: (action: string) => {
      info({
        title: t('common:info', 'Thông tin'),
        message: t('aiAgents:notification.processing', 'Đang {{action}}...', { action }),
        duration: 2000,
      });
    },

    /**
     * Thông báo lưu thành công
     */
    saveSuccess: () => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.saveSuccess', 'Đã lưu thành công'),
      });
    },

    /**
     * Thông báo lưu lỗi
     */
    saveError: (errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.saveError', 'Có lỗi xảy ra khi lưu'),
      });
    },

    /**
     * Thông báo publish thành công
     */
    publishSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.publishSuccess', '{{entityName}} đã được xuất bản thành công', { entityName }),
      });
    },

    /**
     * Thông báo publish lỗi
     */
    publishError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.publishError', 'Có lỗi xảy ra khi xuất bản {{entityName}}', { entityName }),
      });
    },

    /**
     * Thông báo khôi phục thành công
     */
    restoreSuccess: (entityName: string) => {
      success({
        title: t('common:success', 'Thành công'),
        message: t('aiAgents:notification.restoreSuccess', '{{entityName}} đã được khôi phục thành công', { entityName }),
      });
    },

    /**
     * Thông báo khôi phục lỗi
     */
    restoreError: (entityName: string, errorMessage?: string) => {
      showError({
        title: t('common:error', 'Lỗi'),
        message: errorMessage || t('aiAgents:notification.restoreError', 'Có lỗi xảy ra khi khôi phục {{entityName}}', { entityName }),
      });
    },
  };
};

export default useAiAgentNotification;
