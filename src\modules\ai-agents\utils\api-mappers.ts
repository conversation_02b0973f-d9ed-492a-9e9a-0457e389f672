import { TypeAgent } from '../components/agent-add/TypeAgentCard';
import { ModelConfigDto, ProfileDto, TypeAgentDetailDto, TypeAgentListItemDto } from '../types';

/**
 * Interface cho frontend profile format
 */
interface FrontendProfileFormat {
  gender: string;
  birthDate: string;
  position: string;
  education: string;
  skills: string[];
  personality: string[];
  languages: string[];
  country: string;
}

/**
 * Interface cho frontend model config format
 */
interface FrontendModelConfigFormat {
  modelId: string;
  temperature: number;
  topP: number;
  topK: number;
  maxTokens: number;
  providerId?: string;
}

/**
 * Convert TypeAgentListItemDto từ API sang TypeAgent cho frontend
 */
export const mapTypeAgentFromApi = (apiTypeAgent: TypeAgentListItemDto): TypeAgent => {
  return {
    id: apiTypeAgent.id,
    name: apiTypeAgent.name,
    description: apiTypeAgent.description || '',
    countTool: apiTypeAgent.countTool || 0,

    // Config từ API - giữ nguyên format enable*
    config: {
      enableAgentProfileCustomization: apiTypeAgent.config.enableAgentProfileCustomization ?? true,
      enableResourceUsage: apiTypeAgent.config.enableResourceUsage ?? true,
      enableTaskConversionTracking: apiTypeAgent.config.enableTaskConversionTracking ?? false,
      enableOutputToWebsiteLiveChat: apiTypeAgent.config.enableOutputToWebsiteLiveChat ?? true,
      enableDynamicStrategyExecution: apiTypeAgent.config.enableDynamicStrategyExecution ?? false,
      enableMultiAgentCollaboration: apiTypeAgent.config.enableMultiAgentCollaboration ?? false,
      enableOutputToMessenger: apiTypeAgent.config.enableOutputToMessenger ?? true,
      enableOutputToZaloOA: apiTypeAgent.config.enableOutputToZaloOA ?? true,
    },
  };
};

/**
 * Convert TypeAgentDetailDto từ API sang TypeAgent cho frontend
 */
export const mapTypeAgentDetailFromApi = (apiTypeAgent: TypeAgentDetailDto): TypeAgent => {
  return {
    id: apiTypeAgent.id,
    name: apiTypeAgent.name,
    description: apiTypeAgent.description || '',
    countTool: apiTypeAgent.countTool || 0,

    // Config từ API - giữ nguyên format enable*
    config: {
      enableAgentProfileCustomization: apiTypeAgent.config.enableAgentProfileCustomization ?? true,
      enableResourceUsage: apiTypeAgent.config.enableResourceUsage ?? true,
      enableTaskConversionTracking: apiTypeAgent.config.enableTaskConversionTracking ?? false,
      enableOutputToWebsiteLiveChat: apiTypeAgent.config.enableOutputToWebsiteLiveChat ?? true,
      enableDynamicStrategyExecution: apiTypeAgent.config.enableDynamicStrategyExecution ?? false,
      enableMultiAgentCollaboration: apiTypeAgent.config.enableMultiAgentCollaboration ?? false,
      enableOutputToMessenger: apiTypeAgent.config.enableOutputToMessenger ?? true,
      enableOutputToZaloOA: apiTypeAgent.config.enableOutputToZaloOA ?? true,
    },
  };
};

/**
 * Convert array TypeAgentListItemDto từ API sang array TypeAgent
 */
export const mapTypeAgentsFromApi = (apiTypeAgents: TypeAgentListItemDto[]): TypeAgent[] => {
  return apiTypeAgents.map(mapTypeAgentFromApi);
};

/**
 * Convert timestamp từ API (milliseconds) sang Date string cho frontend
 */
export const formatTimestampToDate = (timestamp: number): string => {
  return new Date(timestamp).toISOString().split('T')[0] || '';
};

/**
 * Convert Date string từ frontend sang timestamp cho API
 */
export const formatDateToTimestamp = (dateString: string): number => {
  return new Date(dateString).getTime();
};

/**
 * Map icon name dựa trên type agent name
 */
export const getIconForTypeAgent = (name: string): string => {
  const lowerName = name.toLowerCase();

  if (lowerName.includes('chat') || lowerName.includes('conversation')) {
    return 'message-circle';
  }
  if (lowerName.includes('sales') || lowerName.includes('sell')) {
    return 'trending-up';
  }
  if (lowerName.includes('support') || lowerName.includes('help')) {
    return 'help-circle';
  }
  if (lowerName.includes('document') || lowerName.includes('doc')) {
    return 'document';
  }
  if (lowerName.includes('multi') || lowerName.includes('team')) {
    return 'users';
  }
  if (lowerName.includes('analysis') || lowerName.includes('analytic')) {
    return 'bar-chart';
  }

  return 'user'; // Default icon
};

/**
 * Convert agent profile từ API format sang frontend format
 */
export const mapAgentProfileFromApi = (apiProfile: ProfileDto): FrontendProfileFormat => {
  return {
    gender: apiProfile.gender || 'MALE',
    birthDate: apiProfile.dateOfBirth || '2000-01-01',
    position: apiProfile.position || '',
    education: apiProfile.education || '',
    skills: apiProfile.skills || [],
    personality: apiProfile.personality || [],
    languages: apiProfile.languages || ['Tiếng Việt'],
    country: apiProfile.country || 'Việt Nam',
  };
};

/**
 * Convert agent profile từ frontend format sang API format
 */
export const mapAgentProfileToApi = (frontendProfile: FrontendProfileFormat): ProfileDto => {
  return {
   gender: ['male', 'female', 'other'].includes(frontendProfile.gender as string)
  ? (frontendProfile.gender as 'male' | 'female' | 'other')
  : 'other',
    dateOfBirth: frontendProfile.birthDate,
    position: frontendProfile.position,
    education: frontendProfile.education,
    skills: frontendProfile.skills,
    personality: frontendProfile.personality,
    languages: frontendProfile.languages,
    country: frontendProfile.country,
  };
};

/**
 * Convert model config từ API format sang frontend format
 */
export const mapModelConfigFromApi = (apiModelConfig: ModelConfigDto, modelId?: string): FrontendModelConfigFormat => {
  return {
    modelId: modelId || 'gpt-4o', // modelId comes from separate field now
    temperature: apiModelConfig.temperature || 0.7,
    topP: apiModelConfig.top_p || 0.9,
    topK: apiModelConfig.top_k || 40,
    maxTokens: apiModelConfig.max_tokens || 1000,
    providerId: "", // provider_id is handled separately
  };
};

/**
 * Convert model config từ frontend format sang API format
 */
export const mapModelConfigToApi = (frontendModelConfig: FrontendModelConfigFormat): ModelConfigDto => {
  return {
    temperature: frontendModelConfig.temperature,
    top_p: frontendModelConfig.topP,
    top_k: frontendModelConfig.topK,
    max_tokens: frontendModelConfig.maxTokens,
    // Note: modelId and provider_id are handled separately in model selection scenarios
  };
};
