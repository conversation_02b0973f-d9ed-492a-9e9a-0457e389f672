import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Checkbox,
} from '@/shared/components/common';
import { z } from 'zod';

// Import the correct types from service
import { UpdateTypeAgentParams, AgentTypeStatusEnum, TypeAgentDetail } from '../agent-type/types/type-agent.types';

interface UpdateTypeAgentResponse {
  result?: {
    id: number;
  };
}

interface EditAgentTypeFormProps {
  agentType: TypeAgentDetail;
  onSubmit: (id: number, values: UpdateTypeAgentParams) => Promise<UpdateTypeAgentResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentTypeSchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('type.validation.nameRequired', 'Tên loại agent là bắt buộc'))
    .trim(),
  description: z.string()
    .min(1, t('type.validation.descriptionRequired', 'Mô tả là bắt buộc'))
    .trim(),
  status: z.nativeEnum(AgentTypeStatusEnum, {
    required_error: t('type.validation.statusRequired', 'Trạng thái là bắt buộc'),
  }),
});

const EditAgentTypeForm: React.FC<EditAgentTypeFormProps> = ({ 
  agentType, 
  onSubmit, 
  onCancel, 
  onSuccess 
}) => {
  const { t } = useTranslation(['admin-agent']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Default config states - khởi tạo từ agentType
  const [defaultConfig, setDefaultConfig] = useState(agentType.defaultConfig);

  // Agent Systems config state - khởi tạo từ agentType
  // const [agentSystemsConfig, setAgentSystemsConfig] = useState<AgentSystemConfigData>(() => {
  //   console.log('EditAgentTypeForm - agentType.agentSystems:', agentType.agentSystems);

  //   // Đảm bảo agentSystems luôn là array of strings
  //   let agentSystems: string[] = [];
  //   if (Array.isArray(agentType.agentSystems)) {
  //     agentSystems = agentType.agentSystems.map(item =>
  //       typeof item === 'string' ? item : (item as any)?.id || String(item)
  //     );
  //   }

  //   console.log('EditAgentTypeForm - processed agentSystems:', agentSystems);

  //   return {
  //     agentSystems,
  //   };
  // });

  // Default values for the form - khởi tạo từ agentType
  const defaultValues = React.useMemo(() => ({
    name: agentType.name,
    description: agentType.description,
    status: agentType.status,
  }), [agentType]);

  // Handle default config changes
  const handleDefaultConfigChange = (key: keyof typeof defaultConfig, value: boolean) => {
    setDefaultConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle agent systems config changes
  // const handleAgentSystemsConfigChange = (data: AgentSystemConfigData) => {
  //   setAgentSystemsConfig(data);
  // };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Validate agent systems
      // if (agentSystemsConfig.agentSystems.length === 0) {
      //   NotificationUtil.error({
      //     message: t('type.validation.agentSystemsRequired', 'Ít nhất một agent system là bắt buộc'),
      //   });
      //   setIsSubmitting(false);
      //   return;
      // }

      // Prepare form data
      const agentTypeData: UpdateTypeAgentParams = {
        name: values['name'] as string,
        description: values['description'] as string,
        defaultConfig,
        status: values['status'] as AgentTypeStatusEnum,
        // agentSystems: agentSystemsConfig.agentSystems,
      };

      console.log('Updating agent type data:', agentTypeData);

      // Submit form data
      const updateResult = await onSubmit(agentType.id, agentTypeData);
      console.log('Agent type updated successfully:', updateResult);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error updating agent type form:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Status options
  const statusOptions = [
    { value: AgentTypeStatusEnum.DRAFT, label: t('type.form.draft', 'Nháp') },
    { value: AgentTypeStatusEnum.APPROVED, label: t('type.form.approved', 'Đã duyệt') },
  ];

  return (
    <Card>
      <div className="flex justify-start items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('type.editType', 'Chỉnh sửa Loại Agent')}
        </Typography>
      </div>

      <Form
        schema={updateAgentTypeSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('type.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormItem
            name="name"
            label={t('type.form.name', 'Tên Loại Agent')}
            required
          >
            <Input
              fullWidth
              placeholder={t('type.form.namePlaceholder', 'Nhập tên loại agent')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('type.form.description', 'Mô tả')}
            required
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('type.form.descriptionPlaceholder', 'Nhập mô tả loại agent')}
            />
          </FormItem>

          <FormGrid columns={1} columnsMd={1} columnsSm={1} gap="md">
            <FormItem
              name="status"
              label={t('type.form.status', 'Trạng thái')}
              required
            >
              <Select
                fullWidth
                placeholder={t('type.form.selectStatus', 'Chọn trạng thái')}
                options={statusOptions}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Agent Systems Configuration */}
        


        {/* Default Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('type.form.defaultConfig', 'Cấu hình mặc định')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Checkbox
              checked={defaultConfig.enableAgentProfileCustomization}
              onChange={(checked) => handleDefaultConfigChange('enableAgentProfileCustomization', checked)}
              label={t('type.form.enableAgentProfileCustomization', 'Cho phép tùy chỉnh hồ sơ agent')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToMessenger}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToMessenger', checked)}
              label={t('type.form.enableOutputToMessenger', 'Cho phép xuất ra Messenger')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToWebsiteLiveChat}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToWebsiteLiveChat', checked)}
              label={t('type.form.enableOutputToWebsiteLiveChat', 'Cho phép xuất ra Website Live Chat')}
            />
            <Checkbox
              checked={defaultConfig.enableTaskConversionTracking}
              onChange={(checked) => handleDefaultConfigChange('enableTaskConversionTracking', checked)}
              label={t('type.form.enableTaskConversionTracking', 'Theo dõi chuyển đổi tác vụ')}
            />
            <Checkbox
              checked={defaultConfig.enableResourceUsage}
              onChange={(checked) => handleDefaultConfigChange('enableResourceUsage', checked)}
              label={t('type.form.enableResourceUsage', 'Sử dụng tài nguyên')}
            />
            <Checkbox
              checked={defaultConfig.enableDynamicStrategyExecution}
              onChange={(checked) => handleDefaultConfigChange('enableDynamicStrategyExecution', checked)}
              label={t('type.form.enableDynamicStrategyExecution', 'Thực thi chiến lược động')}
            />
            <Checkbox
              checked={defaultConfig.enableMultiAgentCollaboration}
              onChange={(checked) => handleDefaultConfigChange('enableMultiAgentCollaboration', checked)}
              label={t('type.form.enableMultiAgentCollaboration', 'Hợp tác đa agent')}
            />
            <Checkbox
              checked={defaultConfig.enableOutputToZaloOA}
              onChange={(checked) => handleDefaultConfigChange('enableOutputToZaloOA', checked)}
              label={t('type.form.enableOutputToZaloOA', 'Cho phép xuất ra Zalo OA')}
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('type.common.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting
              ? t('type.form.updating', 'Đang cập nhật loại agent...')
              : t('type.updateType', 'Cập nhật Loại Agent')
            }
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentTypeForm;
