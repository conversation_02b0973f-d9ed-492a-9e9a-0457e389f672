import { Loading } from "@/shared";
import MainLayout from "@/shared/layouts/MainLayout";
import { Suspense, lazy } from "react";
import { RouteObject } from "react-router-dom";

// Lazy load AI pages
const AIAgentsPage = lazy(() => import("../pages/AIAgentsPage"));
const AgentDetailPage = lazy(() => import("../pages/AgentDetailPage"));
const AgentEditPage = lazy(() => import("../pages/AgentEditPage"));
const AgentCategoriesPage = lazy(() => import("../pages/AgentCategoriesPage"));
const AgentCreatePage = lazy(() => import("../pages/AgentCreatePage"));

const agentRoutes: RouteObject[] = [
  {
    path: '/ai-agents',
    element: (
      <MainLayout title="aiAgents:aiAgents">
        <Suspense fallback={<Loading />}>
          <AIAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/:id',
    element: (
      <MainLayout title="aiAgents:common.agentDetail">
        <Suspense fallback={<Loading />}>
          <AgentDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/:id/edit',
    element: (
      <MainLayout title="aiAgents:editAgent.title">
        <Suspense fallback={<Loading />}>
          <AgentEditPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/categories',
    element: (
      <MainLayout title="aiAgents:agentCreatePage.title">
        <Suspense fallback={<Loading />}>
          <AgentCategoriesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/ai-agents/add',
    element: (
      <MainLayout title="aiAgents:agentCreate.title">
        <Suspense fallback={<Loading />}>
          <AgentCreatePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default agentRoutes;
