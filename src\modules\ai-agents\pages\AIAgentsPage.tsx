import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import { ActiveFilters } from '@/modules/components/filters';
import React, { useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentGrid } from '../components';
import { useGetAgents } from '../hooks/useAgent';
import { GetAgentsQueryDto, SortDirection, AgentListItemDto } from '../types';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import { useActiveFilters } from '@/shared/hooks/filters';

// Agent status enum để filter
enum AgentStatus {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Trang hiển thị danh sách AI Agents
 */
const AIAgentsPage: React.FC = () => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const navigate = useNavigate();

  // Định nghĩa columns cho bảng (dù không dùng table nhưng cần cho hook)
  const columns = useMemo(
    () => [
      {
        key: 'id',
        title: t('common:id', 'ID'),
        dataIndex: 'id',
        sortable: true,
      },
      {
        key: 'name',
        title: t('aiAgents:name', 'Tên Agent'),
        dataIndex: 'name',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        sortable: true,
      },
    ],
    [t]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: AgentStatus.ALL },
      { id: 'active', label: t('common:active'), icon: 'check', value: AgentStatus.ACTIVE },
      { id: 'inactive', label: t('common:inactive'), icon: 'eye-off', value: AgentStatus.INACTIVE },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): GetAgentsQueryDto => {
      const queryParams: GetAgentsQueryDto = {
        page: params.page,
        limit: params.pageSize,
        ...(params.searchTerm && { search: params.searchTerm }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortDirection && { sortDirection: params.sortDirection }),
      };

      // Xử lý filter status
      if (params.filterValue !== AgentStatus.ALL) {
        queryParams.active = params.filterValue === AgentStatus.ACTIVE;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AgentListItemDto, GetAgentsQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: agentsResponse, isLoading, error, refetch } = useGetAgents(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (agentsResponse) {
      // Sử dụng tham chiếu để tránh vòng lặp vô hạn
      updateTableDataRef.current(agentsResponse.result, isLoading);
    }
  }, [agentsResponse, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [AgentStatus.ACTIVE]: t('common:active', 'Hoạt động'),
        [AgentStatus.INACTIVE]: t('common:inactive', 'Không hoạt động'),
      },
      t,
    });

  const handleAddAgent = () => {
    navigate('/ai-agents/add');
  };



  // Extract data từ API response
  const agents = agentsResponse?.result?.items || [];
  const totalItems = agentsResponse?.result?.meta?.totalItems || 0;

  // Wrapper cho handlePageChange và handlePageSizeChange
  const handlePageChangeWrapper = useCallback((newPage: number) => {
    dataTable.tableData.handlePageChange(newPage, dataTable.tableData.pageSize);
  }, [dataTable.tableData]);

  const handlePageSizeChangeWrapper = useCallback((newPageSize: number) => {
    dataTable.tableData.handlePageChange(1, newPageSize);
  }, [dataTable.tableData]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <PageWrapper>
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAddAgent}
          items={dataTable.menuItems}
          showDateFilter={false}
          showColumnFilter={false}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </PageWrapper>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <PageWrapper>
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAddAgent}
          items={dataTable.menuItems}
          showDateFilter={false}
          showColumnFilter={false}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common:error', 'Lỗi')}
          description={t('aiAgents:list.loadError', 'Không thể tải danh sách AI Agents. Vui lòng thử lại.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common:retry', 'Thử lại')}
            </Button>
          }
        />
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAddAgent}
        items={dataTable.menuItems}
        showDateFilter={false}
        showColumnFilter={false}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {agents.length > 0 ? (
        <>
          <AgentGrid agents={agents} />

          {/* Pagination */}
          {totalItems > dataTable.tableData.pageSize && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={dataTable.tableData.currentPage}
                totalItems={totalItems}
                itemsPerPage={dataTable.tableData.pageSize}
                onPageChange={handlePageChangeWrapper}
                onItemsPerPageChange={handlePageSizeChangeWrapper}
                itemsPerPageOptions={[6, 12, 24, 48]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="robot"
          title={t('aiAgents:list.noAgents', 'Không có AI Agents')}
          description={
            dataTable.tableData.searchTerm
              ? t('aiAgents:list.noSearchResults', 'Không tìm thấy AI Agents phù hợp với từ khóa tìm kiếm.')
              : t('aiAgents:list.noAgentsDescription', 'Hiện tại chưa có AI Agents nào. Hãy tạo Agent đầu tiên của bạn.')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('aiAgents:list.createFirst', 'Tạo AI Agent đầu tiên')}
            </Button>
          }
        />
      )}
    </PageWrapper>
  );
};

export default AIAgentsPage;
