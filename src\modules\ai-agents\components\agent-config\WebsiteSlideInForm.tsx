import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  Icon,
  IconCard,
  Input,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useGetWebsites, useCreateWebsite } from '@/modules/integration/website/hooks/useWebsite';
import { WebsiteDto, WebsiteQueryDto, CreateWebsiteDto, WebsiteSortBy, SortDirection } from '@/modules/integration/website/types/website.types';
import { useAgentWebsites, useAddWebsites } from '@/modules/ai-agents/hooks/useAgentIntegration';
import { useAiAgentNotification } from '@/modules/ai-agents/hooks/useAiAgentNotification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Website - mapped từ WebsiteDto API
 */
interface Website {
  id: string;
  name: string;
  url: string;
  icon?: string;
  category?: string;
  isConnected?: boolean;
  status?: 'active' | 'pending' | 'error';
}

/**
 * Props cho component WebsiteSlideInForm
 */
interface WebsiteSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi lưu dữ liệu (chỉ dùng trong create mode)
   */
  onSave?: (selectedIds: string[]) => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';
}

/**
 * Component form trượt để chọn các website để tích hợp - sử dụng API thực
 */
const WebsiteSlideInForm: React.FC<WebsiteSlideInFormProps> = ({
  isVisible,
  onClose,
  onSave,
  agentId,
  mode,
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);
  const {
    createSuccess,
    createError,
    updateSuccess,
    updateError,
    validationError,
    warning
  } = useAiAgentNotification();

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // API hooks
  const { data: agentWebsitesResponse } = useAgentWebsites(agentId && mode === 'edit' ? agentId : '');
  const addWebsitesMutation = useAddWebsites();

  // Lấy danh sách websites đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedWebsiteIds = useMemo(() => {
    return agentWebsitesResponse?.websites?.map(website => website.id) || [];
  }, [agentWebsitesResponse?.websites]);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<WebsiteQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: WebsiteSortBy.HOST,
    sortDirection: SortDirection.ASC,
  });

  // Khởi tạo selectedIds từ agent websites khi component mount
  useEffect(() => {
    if (mode === 'edit' && selectedWebsiteIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedWebsiteIds);
    }
  }, [selectedWebsiteIds, mode, selectedIds.length]);

  // State cho thêm website mới
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newWebsiteName, setNewWebsiteName] = useState<string>('');
  const [newWebsiteUrl, setNewWebsiteUrl] = useState<string>('');

  // API hooks
  const { data: websitesData, isLoading, refetch } = useGetWebsites(queryParams);
  const createWebsiteMutation = useCreateWebsite();

  // Transform API data to component format
  const websites: Website[] = useMemo(() => {
    if (!websitesData?.result?.items) return [];

    return websitesData.result.items.map((website: WebsiteDto): Website => ({
      id: website.id,
      name: website.host, // API chỉ có host, không có name riêng
      url: `https://${website.host}`,
      icon: website.logo || "",
      category: "", // API không có category
      isConnected: !!website.agentId,
      status: website.verify ? 'active' : 'pending',
    }));
  }, [websitesData]);

  // Pagination data
  const totalItems = websitesData?.result?.meta?.totalItems || 0;
  const currentPage = queryParams.page || 1;
  const itemsPerPage = queryParams.limit || 10;

  // Cấu hình cột cho bảng - sử dụng useMemo để tránh re-render
  const columns: TableColumn<Website>[] = useMemo(() => [
    {
      key: 'website',
      title: t('aiAgents:websiteSlideInForm.website'),
      dataIndex: 'name',
      width: '50%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.icon ? (
            <img
              src={record.icon}
              alt={record.name}
              className="w-10 h-10 rounded-md mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-md bg-green-100 flex items-center justify-center mr-3">
              <Icon name="globe" size="md" className="text-green-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.url}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: t('aiAgents:websiteSlideInForm.category'),
      dataIndex: 'category',
      width: '25%',
    },
    {
      key: 'status',
      title: t('aiAgents:websiteSlideInForm.status'),
      dataIndex: 'status',
      width: '25%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              {t('aiAgents:websiteSlideInForm.connected')}
            </span>
          ) : record.status === 'pending' ? (
            <span className="text-yellow-500 text-sm flex items-center">
              <Icon name="clock" size="sm" className="mr-1" />
              {t('aiAgents:websiteSlideInForm.processing')}
            </span>
          ) : record.status === 'error' ? (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              {t('aiAgents:websiteSlideInForm.connectionError')}
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              {t('aiAgents:websiteSlideInForm.notConnected')}
            </span>
          )}
        </div>
      ),
    },
  ], [t]);







  // Xử lý tìm kiếm - sử dụng useCallback
  const handleSearch = useCallback((term: string) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      search: term,
      page: 1,
    }));
  }, []);

  // Xử lý thay đổi trang - sử dụng useCallback
  const handlePageChange = useCallback((page: number) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      page,
    }));
  }, []);

  // Xử lý thay đổi số lượng item trên trang - sử dụng useCallback
  const handleItemsPerPageChange = useCallback((value: number) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  }, []);

  // Xử lý thay đổi sắp xếp - sử dụng useCallback
  const handleSortChange = useCallback((column: string, direction: 'ASC' | 'DESC') => {
    const sortBy = column === 'name' ? WebsiteSortBy.HOST : WebsiteSortBy.HOST;
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      sortBy,
      sortDirection: direction as SortDirection,
    }));
  }, []);

  // Xử lý thêm website mới (API thực) - sử dụng useCallback
  const handleAddWebsite = useCallback(async () => {
    if (!newWebsiteName || !newWebsiteUrl) {
      validationError(t('aiAgents:websiteSlideInForm.pleaseEnterAllInfo'));
      return;
    }

    // Kiểm tra URL hợp lệ và extract host
    let host: string;
    try {
      const url = new URL(newWebsiteUrl);
      host = url.hostname;
    } catch {
      validationError(t('aiAgents:websiteSlideInForm.invalidUrl'));
      return;
    }

    try {
      // Gọi API để tạo website mới
      const createData: CreateWebsiteDto = {
        websiteName: newWebsiteName,
        host: host,
      };

      await createWebsiteMutation.mutateAsync(createData);

      // Reset form
      setNewWebsiteName('');
      setNewWebsiteUrl('');
      setShowAddForm(false);

      // Refetch data để cập nhật danh sách
      refetch();

      createSuccess('Website');
    } catch (error) {
      console.error('Error adding website:', error);
      createError('Website', error instanceof Error ? error.message : undefined);
    }
  }, [newWebsiteName, newWebsiteUrl, validationError, t, createWebsiteMutation, refetch, createSuccess, createError]);

  // Xử lý lưu - phân biệt theo mode - sử dụng useCallback
  const handleSave = useCallback(async () => {
    if (mode === 'edit') {
      // Edit mode: gọi API
      if (!agentId) {
        validationError(t('aiAgents:websiteSlideInForm.cannotSaveInThisMode'));
        return;
      }

      setIsSubmitting(true);
      try {
        // Gọi API để cập nhật websites cho agent
        const response = await addWebsitesMutation.mutateAsync({
          agentId,
          data: { websiteIds: selectedIds }
        });

        // Xử lý response chi tiết
        if (response.integratedCount > 0) {
          updateSuccess(t('aiAgents:websiteSlideInForm.integratedSuccessfully', { count: response.integratedCount }));
        }

        if (response.skippedCount > 0) {
          const errorDetails = response.details
            .filter(detail => detail.status === 'error' || detail.status === 'skipped')
            .map(detail => detail.error || 'Website đã được tích hợp với agent khác')
            .join(', ');

          warning({
            message: t('aiAgents:websiteSlideInForm.cannotIntegrate', { count: response.skippedCount, details: errorDetails }),
            duration: 8000,
          });
        }

        if (response.integratedCount === 0 && response.skippedCount > 0) {
          updateError('Website', t('aiAgents:websiteSlideInForm.noWebsiteIntegratedSuccessfully'));
        }

        // Gọi callback để parent component biết có thay đổi
        onSave?.(selectedIds);
        onClose();
      } catch (error) {
        console.error('Website integration error:', error);
        updateError('Website', error instanceof Error ? error.message : t('aiAgents:websiteSlideInForm.updateIntegrationError'));
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: gọi callback để cập nhật parent component
      if (onSave) {
        onSave(selectedIds);
      }
      updateSuccess(t('aiAgents:websiteSlideInForm.selectedWebsiteIntegration'));
      onClose();
    }
  }, [mode, agentId, selectedIds, validationError, t, addWebsitesMutation, updateSuccess, warning, updateError, onSave, onClose]);

  // Xử lý đóng form - không cần confirm
  const handleClose = useCallback(() => {
    setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, search: '' }));
    setShowAddForm(false);
    onClose();
  }, [onClose]);



  // Xử lý cancel add form
  const handleCancelAddForm = useCallback(() => {
    setShowAddForm(false);
  }, []);

  // Xử lý sort change cho table
  // const handleTableSortChange = useCallback((column: string | undefined, order: 'asc' | 'desc') => {
  //   if (column) {
  //     handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
  //   }
  // }, [handleSortChange]);

  // Xử lý selection change
  const handleSelectionChange = useCallback((keys: React.Key[]) => {
    setSelectedIds(keys as string[]);
  }, []);

  // Xử lý pagination change
  const handlePaginationChange = useCallback((page: number, pageSize: number) => {
    handlePageChange(page);
    if (pageSize !== itemsPerPage) {
      handleItemsPerPageChange(pageSize);
    }
  }, [handlePageChange, handleItemsPerPageChange, itemsPerPage]);

  // Các menu items cho MenuIconBar - sử dụng useMemo
  const menuItems = useMemo(() => [
    {
      id: 'sort',
      label: t('aiAgents:websiteSlideInForm.sortBy'),
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-host',
      label: t('aiAgents:websiteSlideInForm.host'),
      onClick: () => handleSortChange('host', queryParams.sortDirection === SortDirection.ASC ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-created',
      label: t('aiAgents:websiteSlideInForm.createdDate'),
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({
          ...prev,
          sortBy: WebsiteSortBy.CREATED_AT,
          sortDirection: prev.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC,
        }));
      },
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: t('aiAgents:websiteSlideInForm.filterBy'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: t('aiAgents:websiteSlideInForm.all'),
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => {
          const { verify, ...rest } = prev;
          void verify; // Suppress unused variable warning
          return rest;
        });
      },
    },
    {
      id: 'filter-verified',
      label: t('aiAgents:websiteSlideInForm.verified'),
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, verify: true }));
      },
    },
    {
      id: 'filter-not-verified',
      label: t('aiAgents:websiteSlideInForm.notVerified'),
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, verify: false }));
      },
    },
  ], [t, handleSortChange, queryParams.sortDirection]);

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:websiteSlideInForm.title')}</Typography>
          {/* Nút lưu */}
        <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('aiAgents:websiteSlideInForm.cancel')}
            onClick={handleClose}
            disabled={isSubmitting}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('aiAgents:websiteSlideInForm.save')}
            onClick={handleSave}
            disabled={isLoading || isSubmitting}
            isLoading={isSubmitting}
          />
         
        
        </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Form thêm website mới */}
        {showAddForm && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center mb-3">
              <Typography variant="subtitle1">{t('aiAgents:websiteSlideInForm.addNewWebsite')}</Typography>
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelAddForm}
                >
                  {t('aiAgents:websiteSlideInForm.cancel')}
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleAddWebsite}
                  disabled={!newWebsiteName || !newWebsiteUrl}
                  isLoading={createWebsiteMutation.isPending}
                >
                  {t('aiAgents:websiteSlideInForm.add')}
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <label htmlFor="websiteName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('aiAgents:websiteSlideInForm.websiteName')}
                </label>
                <Input
                  id="websiteName"
                  value={newWebsiteName}
                  onChange={(e) => setNewWebsiteName(e.target.value)}
                  placeholder={t('aiAgents:websiteSlideInForm.enterWebsiteName')}
                  fullWidth
                />
              </div>
              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('aiAgents:websiteSlideInForm.url')}
                </label>
                <Input
                  id="websiteUrl"
                  value={newWebsiteUrl}
                  onChange={(e) => setNewWebsiteUrl(e.target.value)}
                  placeholder={t('aiAgents:websiteSlideInForm.enterWebsiteUrl')}
                  fullWidth
                />
              </div>
            </div>
          </div>
        )}

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<Website>
            columns={columns}
            data={websites}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            // onSortChange={handleTableSortChange}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: handleSelectionChange,
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePaginationChange,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </div>

        
      </Card>
    </SlideInForm>
  );
};

export default WebsiteSlideInForm;
